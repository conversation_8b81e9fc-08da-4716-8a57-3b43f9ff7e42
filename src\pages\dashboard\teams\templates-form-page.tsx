import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useSearchParams, useLocation, useParams } from 'react-router-dom';
import { TeamForm } from 'src/sections/teams/form/team-form';
import { useMyTeamTemplatesApi } from 'src/services/api/use-my-teams-api';
import { useTeamTeamplatessApi } from 'src/services/api/use-teams-api';

const TemplatesForm = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  // Get team type from URL params or location state
  const teamType = (searchParams.get('type') || location.state?.teamType || 'AUTO') as
    | 'AUTO'
    | 'MANUAL';

  const { useGetTeamTeamplates } = useTeamTeamplatessApi();

  // Fetch team data when editing
  const { data: team } = useGetTeamTeamplates(id!);
  console.log('team', team);

  return (
    <>
      <Helmet>
        <title>{id ? 'create team template' : 'edut team template'}</title>
      </Helmet>

      <TeamForm team={team ?? null} initialTeamType={teamType} />
    </>
  );
};

export default TemplatesForm;
