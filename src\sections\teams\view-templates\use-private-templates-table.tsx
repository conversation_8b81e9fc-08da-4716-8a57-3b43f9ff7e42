import { Icon } from '@iconify/react';
import { Avatar, Box, Chip, IconButton, Typography } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import LongMenu from 'src/components/long-menu';
import { useTable } from 'src/components/table';
import { AppTablePropsType } from 'src/components/table/app-table/types/app-table';
import { paths } from 'src/routes/paths';
import { TeamTeamplatesType, useTeamTeamplatessApi } from 'src/services/api/use-teams-api';
import { fDate } from 'src/utils/format-time';

export const usePrivateTemplatesTable = (privateTemplates: TeamTeamplatesType[]) => {
  const { t } = useTranslation();
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  const [templateToDelete, setTemplateToDelete] = useState<TeamTeamplatesType | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);

  const { useDeleteTeamTeamplates } = useTeamTeamplatessApi();
  const { mutate: deleteTeamTemplate, isPending: isPendingDelete } = useDeleteTeamTeamplates();
  const navigate = useNavigate();
  // Handle file selection
  const handleSelectFile = (id: string) => {
    setSelectedFiles((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleSelectAllFiles = (checked: boolean) => {
    if (checked) {
      setSelectedFiles(privateTemplates.map((template) => template.id.toString()));
    } else {
      setSelectedFiles([]);
    }
  };

  const handleDeleteTemplate = (id: number | string) => {
    if (id)
      deleteTeamTemplate(id, {
        onSuccess: () => {
          handleCloseConfirmDialog();
        },
      });
  };

  const handleOpenConfirmDialog = (template: TeamTeamplatesType) => {
    setTemplateToDelete(template);
    setOpenConfirmDialog(true);
  };

  const handleCloseConfirmDialog = () => {
    setOpenConfirmDialog(false);
    setTemplateToDelete(null);
  };

  const handleConfirmDelete = () => {
    if (templateToDelete?.id) {
      handleDeleteTemplate(templateToDelete.id);
    }
  };

  // Table head labels
  const headLabels = [
    { id: 'name', label: t('table.name') },
    { id: 'dateCreated', label: t('table.dateCreated'), align: 'center' },
    { id: 'type', label: t('table.type'), align: 'center' },
    { id: 'category', label: t('table.category'), align: 'center' },
    { id: 'tools', label: t('table.tools'), align: 'center' },
    { id: 'llmModel', label: t('table.llmModel'), align: 'center' },
    { id: 'status', label: t('table.status'), align: 'center' },
    { id: 'action', label: t('table.action'), align: 'center' },
  ];

  const getMenuOptions = (template: TeamTeamplatesType) => [
    {
      label: t('buttons.edit'),
      icon: 'eva:edit-fill',
      onClick: () => {
        // TODO: implement edit navigation/handler
        navigate(paths.dashboard.teams.edit.replace(':id', template.id.toString()));
      },
      color: 'inherit',
    },

    {
      label: t('buttons.clone'),
      icon: 'material-symbols-light:cyclone',
      onClick: () =>
        navigate(paths.dashboard.teams.clone(template?.id), {
          state: { name: template?.name },
        }),
      color: 'primary.main',
    },
    {
      label: t('buttons.delete'),
      icon: 'eva:trash-2-outline',
      onClick: () => handleOpenConfirmDialog(template),
      color: 'error.main',
    },
  ];
  // Table columns configuration
  const columns: AppTablePropsType<TeamTeamplatesType>['columns'] = [
    {
      name: 'name',
      PreviewComponent: ({ name }) => (
        <Box display="flex" alignItems="center" gap={1.5}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: 'text.primary',
              color: 'background.paper',
              fontSize: '0.875rem',
              fontWeight: 600,
            }}
          />
          <Typography variant="body2" fontWeight={500} color="text.primary">
            {name}
          </Typography>
        </Box>
      ),
    },
    {
      name: 'createdAt',
      PreviewComponent: ({ createdAt }) => (
        <Typography variant="body2" color="text.secondary">
          {fDate(createdAt)}
        </Typography>
      ),
    },
    {
      name: 'type',
      PreviewComponent: ({ type }) => (
        <Typography variant="body2" color="text.secondary">
          {type}
        </Typography>
      ),
    },
    {
      name: 'category',
      PreviewComponent: ({ category }) => (
        <Typography variant="body2" color="text.secondary">
          {category.name}
        </Typography>
      ),
    },
    {
      name: 'category.id',
      PreviewComponent: (data) => (
        <Box display="flex" alignItems="center" justifyContent="center">
          <Icon icon="mdi:tools" width={16} height={16} style={{ color: '#D32F2F' }} />
        </Box>
      ),
    },
    {
      name: 'model',
      PreviewComponent: ({ model }) => (
        <Typography variant="body2" color="text.secondary">
          {model?.replace(/_/g, ' ')}
        </Typography>
      ),
    },
    {
      name: 'status',
      PreviewComponent: (
        { visibility } // Destructure visibility directly from props
      ) => (
        <Chip
          label={visibility}
          size="small"
          sx={{
            bgcolor: visibility === 'PRIVATE' ? '#FFEBEE' : '#E8F5E8',
            color: visibility === 'PRIVATE' ? '#D32F2F' : '#2E7D32',
            fontWeight: 500,
            fontSize: '0.75rem',
            height: '24px',
            '& .MuiChip-label': {
              px: 1.5,
            },
          }}
        />
      ),
    },
    {
      name: 'status',
      cellSx: { width: '50px' },
      PreviewComponent: (template) => <LongMenu options={getMenuOptions(template)} />,
    },
  ];

  // Select configuration
  const selectConfig = {
    idPath: 'id' as keyof TeamTeamplatesType,
    handleSelectRow: (row: TeamTeamplatesType) => handleSelectFile(row.id.toString()),
    handleSelectAllRows: (ids: string[]) => (checked: boolean) => handleSelectAllFiles(checked),
    selected: selectedFiles,
    rowCount: privateTemplates.length,
    numSelected: selectedFiles.length,
    selectedRowsActions: [],
  };

  return {
    privateTemplates,
    selectedFiles,
    handleSelectFile,
    handleSelectAllFiles,
    columns,
    headLabels,
    selectConfig,
    openConfirmDialog,
    isPendingDelete,
    handleCloseConfirmDialog,
    handleDeleteTemplate,
    handleOpenConfirmDialog,
    handleConfirmDelete,
  };
};
