import { Box, Divider, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AppButton } from 'src/components/common/app-button';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table';
import { AppContainer } from 'src/components/common';

import useScheduledTasksTiew from './use-scheduled-tasks-view';

const ScheduledTasksView = () => {
  const { t } = useTranslation();
  const {
    headLabels,
    tasksData,
    isLoading,
    columns,
    table,
    openDeleteDialog,
    setOpenDeleteDialog,
    handleConfirmDelete,
    isDeleting,
    openToggleDialog,
    setOpenToggleDialog,
    selectedTask,
    handleConfirmToggle,
    isToggling,
  } = useScheduledTasksTiew();

  return (
    <AppContainer>
      <Typography variant="h3" fontWeight={800} color="text.primary">
        {t('pages.scheduledTasks.title')}
      </Typography>
      <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)' }}>
        {t('pages.scheduledTasks.description')}
      </Typography>
      <Divider sx={{ my: '24px' }} />
      <AppTable
        headLabels={headLabels}
        dataCount={tasksData?.total || 0}
        isLoading={isLoading}
        data={tasksData?.tasksScheduled || []}
        columns={columns}
        table={table}
        noDataLabel={t('pages.scheduledTasks.noTasksFound')}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: -1 }}>
            <Iconify icon="solar:trash-bin-trash-bold" width={44} color="error.main" />
            <Typography variant="h6">{t('dialogs.deleteScheduledTask')}</Typography>
          </Box>
        }
        content={t('components.agents.deleteScheduledTaskConfirm')}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label={t('components.chat.cancel')}
              variant="outlined"
              sx={{ backgroundColor: 'white' }}
              onClick={() => setOpenDeleteDialog(false)}
              size="large"
            />
            <AppButton
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleConfirmDelete}
              isLoading={isDeleting}
              size="large"
            />
          </Box>
        }
      />

      {/* Toggle Status Confirmation Dialog */}
      <ConfirmDialog
        open={openToggleDialog}
        onClose={() => setOpenToggleDialog(false)}
        title={
          <Typography variant="h4" textAlign="center">
            {selectedTask?.isActive
              ? t('components.agents.pauseScheduledTask')
              : t('components.agents.resumeScheduledTask')}
          </Typography>
        }
        content={
          selectedTask?.isActive
            ? t('components.agents.pauseTaskContent')
            : t('components.agents.resumeTaskContent')
        }
        icon={
          <Iconify
            icon={selectedTask?.isActive ? 'eva:pause-circle-fill' : 'eva:play-circle-fill'}
            sx={{
              mx: 'auto',
              width: '70px',
              height: '70px',
              color: selectedTask?.isActive ? 'warning.main' : 'success.main',
            }}
          />
        }
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <AppButton
              label={t('components.chat.cancel')}
              variant="outlined"
              sx={{ backgroundColor: 'white' }}
              onClick={() => setOpenToggleDialog(false)}
              size="large"
            />
            <AppButton
              label={selectedTask?.isActive ? t('buttons.pause') : t('buttons.resume')}
              variant="contained"
              color={selectedTask?.isActive ? 'warning' : 'success'}
              onClick={handleConfirmToggle}
              isLoading={isToggling}
              size="large"
            />
          </Box>
        }
      />
    </AppContainer>
  );
};

export default ScheduledTasksView;
