import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { ConnectionsIntegrationView } from 'src/sections/connections-integrations/view/connections-integrations-view';

// ----------------------------------------------------------------------

export default function KnowledgeBasePage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{t('pages.profile.knowledgeBase')}</title>
      </Helmet>

      <ConnectionsIntegrationView />
    </>
  );
}
