# Translation System Documentation

This document explains how to use the translation system in the Midad application.

## Overview

The application uses `react-i18next` for internationalization, supporting English (en) and Arabic (ar) languages with RTL support for Arabic.

## File Structure

```
src/locales/
├── en.ts          # English translations
├── ar.ts          # Arabic translations
├── types.d.ts     # TypeScript types for translations
└── README.md      # This documentation
```

## Usage

### Basic Translation

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  
  return (
    <Typography>
      {t('components.common.search')}
    </Typography>
  );
}
```

### Translation with Parameters

```tsx
// For translations with placeholders
{t('components.noData.listAvailableMessage', { item: t('components.teams.title') })}
```

### Form Field Translations

Use the `useFormTranslations` hook for form fields:

```tsx
import { useFormTranslations } from 'src/hooks/use-form-translations';

function MyForm() {
  const { getTranslatedFieldConfig } = useFormTranslations();
  const nameConfig = getTranslatedFieldConfig('name');
  
  return (
    <TextField
      label={nameConfig?.label}
      placeholder={nameConfig?.placeholder}
      helperText={nameConfig?.helperText}
    />
  );
}
```

### Validation Messages

For form validation, use translation keys in your schemas:

```tsx
const schema = z.object({
  name: z.string().min(1, 'validation.nameRequired'),
  email: z.string().email('validation.emailValid'),
});
```

## Translation Keys Structure

### Pages
- `pages.dashboard.*` - Dashboard page translations
- `pages.auth.*` - Authentication pages
- `pages.profile.*` - Profile pages
- `pages.error.*` - Error pages

### Components
- `components.common.*` - Common UI elements
- `components.buttons.*` - Button labels
- `components.tables.*` - Table headers and labels
- `components.navigation.*` - Navigation items
- `components.chat.*` - Chat interface
- `components.agents.*` - Agent-related components
- `components.teams.*` - Team-related components
- `components.notifications.*` - Notification components

### Forms and Validation
- `forms.*` - Form labels, placeholders, helpers
- `validation.*` - Validation error messages
- `loading.*` - Loading states and messages

### Status and Messages
- `status.*` - Status labels (active, inactive, etc.)
- `messages.*` - System messages
- `filters.*` - Filter options
- `dialogs.*` - Dialog messages

### UI Elements
- `ui.*` - General UI text
- `auth.*` - Authentication flow text
- `error.*` - Error messages
- `settings.*` - Settings page
- `categories.*` - Categories page
- `table.*` - Table column headers
- `buttons.*` - Button labels

## Adding New Translations

1. Add the key to `en.ts`:
```typescript
export const en = {
  // ... existing translations
  myNewSection: {
    title: 'My New Title',
    description: 'My description',
  },
};
```

2. Add the corresponding Arabic translation to `ar.ts`:
```typescript
export const ar: TranslationKeys = {
  // ... existing translations
  myNewSection: {
    title: 'عنواني الجديد',
    description: 'وصفي',
  },
};
```

3. Use in components:
```tsx
{t('myNewSection.title')}
```

## Best Practices

1. **Consistent Key Structure**: Use dot notation for nested keys
2. **Descriptive Keys**: Make keys self-explanatory
3. **Avoid Hardcoded Text**: Always use translation keys for user-facing text
4. **Parameter Support**: Use interpolation for dynamic content
5. **Context-Specific Keys**: Group related translations together

## Language Switching

The language can be switched using the language selector in the header. The selected language is stored in localStorage and persists across sessions.

## RTL Support

Arabic language automatically enables RTL (Right-to-Left) layout support throughout the application.
