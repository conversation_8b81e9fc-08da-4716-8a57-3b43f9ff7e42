import { zodResolver } from '@hookform/resolvers/zod';
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  MenuItem,
  Typography,
} from '@mui/material';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { AppButton } from 'src/components/common';
import { Field, Form } from 'src/components/hook-form';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import {
  ScheduledTaskItem,
  scheduleTaskEndpoints,
  useTasksSchedularApi,
} from 'src/services/api/use-task-schedular-api';
import * as z from 'zod';

// ----------------------------------------------------------------------

const schema = z
  .object({
    task: z.string().min(1, 'Task is required'),
    repeatType: z.enum(['NONE', 'HOURLY', 'MONTHLY', 'WEEKLY', 'DAILY'], {
      required_error: 'Repeat type is required',
    }),
    startDate: z.date({
      required_error: 'Start date is required',
    }),
    endDate: z.date().optional(), // will validate conditionally
    interval: z.number().min(1, 'Interval must be at least 1').optional(),
  })
  .superRefine((data, ctx) => {
    if (data.repeatType !== 'NONE') {
      if (!data.endDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'End date is required when repeat type is not None',
          path: ['endDate'],
        });
      } else if (data.endDate <= data.startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'End date must be greater than start date',
          path: ['endDate'],
        });
      }
    }
  });

type FormValues = z.infer<typeof schema>;

interface ScheduledTaskDialogProps {
  open: boolean;
  onClose: () => void;
  // Creation props (optional in edit mode)
  message?: string;
  chatId?: string | number;
  // Edit mode support
  mode?: 'create' | 'edit';
  scheduledTask?: ScheduledTaskItem | null;
}

const intervalMaxByType: Record<FormValues['repeatType'], number> = {
  NONE: 1,
  HOURLY: 24,
  DAILY: 31, // max 30 days
  WEEKLY: 4, // max 12 weeks
  MONTHLY: 12, // max 12 months
};

const repeatTypeOptions = [
  { value: 'NONE', label: 'None' },
  { value: 'HOURLY', label: 'Hourly' },
  { value: 'DAILY', label: 'Daily' },
  { value: 'WEEKLY', label: 'Weekly' },
  { value: 'MONTHLY', label: 'Monthly' },
];

// ----------------------------------------------------------------------

export default function ScheduledTaskDialog({
  open,
  onClose,
  message,
  chatId,
  mode = 'create',
  scheduledTask,
}: ScheduledTaskDialogProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { useShceduledTask, useUpdateScheduledTask } = useTasksSchedularApi();

  const methods = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      task: message || scheduledTask?.task?.content || '',
      repeatType: (scheduledTask?.repeatType as FormValues['repeatType']) || 'DAILY',
      startDate: scheduledTask?.startDate ? new Date(scheduledTask.startDate) : new Date(),
      endDate: scheduledTask?.endDate ? new Date(scheduledTask.endDate) : new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      interval: scheduledTask?.interval ?? 1,
    },
  });

  const { handleSubmit, reset, watch } = methods;
  const repeatType = watch('repeatType');

  // Only reset interval automatically on repeatType changes for CREATE mode
  useEffect(() => {
    if (mode !== 'edit') {
      methods.setValue('interval', 0 as any);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [repeatType, mode]);

  const scheduledTaskMutation = useShceduledTask();
  const updateScheduledTaskMutation = useUpdateScheduledTask(scheduledTask?.id ?? '');

  const onSubmit = handleSubmit(async (data) => {
    try {
      const basePayload: any = {
        repeatType: data.repeatType,
        startDate: data.startDate.toISOString(),
      };

      if (data.repeatType !== 'NONE') {
        basePayload.endDate = data.endDate?.toISOString();
        if (data.interval) basePayload.interval = data.interval;
      }

      if (mode === 'edit' && scheduledTask?.id) {
        await updateScheduledTaskMutation.mutateAsync(basePayload, {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [scheduleTaskEndpoints.list + 'list'] });
            handleClose?.();
          },
        });
      } else {
        const createPayload: any = {
          ...basePayload,
          chatId,
          task: message,
        };

        await scheduledTaskMutation.mutateAsync(createPayload, {
          onSuccess: () => {
            handleClose?.();
            navigate(paths.dashboard.scheduled.root);
          },
        });
      }
    } catch (error) {
      console.error('Error scheduling task:', error);
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  // Keep task value in sync when message changes (create mode)
  React.useEffect(() => {
    if (message && open && mode !== 'edit') {
      methods.setValue('task', message);
    }
  }, [message, open, mode, methods]);

  // Refresh default values when dialog opens
  React.useEffect(() => {
    if (open) {
      if (mode === 'edit' && scheduledTask) {
        reset({
          task: scheduledTask.task?.content || '',
          repeatType: scheduledTask.repeatType as FormValues['repeatType'],
          startDate: new Date(scheduledTask.startDate),
          endDate: scheduledTask.endDate ? new Date(scheduledTask.endDate) : undefined,
          interval: scheduledTask.interval ?? 1,
        });
      } else {
        reset({
          task: message || '',
          repeatType: 'DAILY',
          startDate: new Date(), // always refresh to "now"
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          interval: 1,
        });
      }
    }
  }, [open, message, mode, scheduledTask, reset]);

  const isEdit = mode === 'edit';

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 4,
          backgroundColor: '#F0F0F1',
          border: '1px solid',
          borderColor: 'divider',
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={handleClose}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          color: 'text.secondary',
          bgcolor: 'action.hover',
          '&:hover': {
            bgcolor: 'action.selected',
            color: 'text.primary',
          },
          width: 36,
          height: 36,
          zIndex: 1,
        }}
      >
        <Iconify icon="eva:close-fill" width={20} height={20} />
      </IconButton>

      {/* Dialog content */}
      <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
        <Typography variant="h4" component="div" color="text.primary">
          {isEdit ? 'Update Scheduled Task' : 'Schedule Task'}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          {isEdit ? 'Modify the recurring task schedule' : 'Set up a recurring task schedule'}
        </Typography>
      </DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent sx={{ px: 3, pb: 2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Repeat Type Field */}
            <Field.Select
              sx={{ mt: '32px', backgroundColor: 'white', borderRadius: '16px' }}
              name="repeatType"
              label="Repeat Type"
              fullWidth
            >
              {repeatTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Field.Select>

            {/* Date Fields */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Field.DatePicker
                dateAndTime
                name="startDate"
                label="Start Date"
                sx={{ flex: 1, backgroundColor: 'white' }}
              />
              {repeatType !== 'NONE' && (
                <Field.DatePicker
                  dateAndTime
                  name="endDate"
                  label="End Date"
                  sx={{ flex: 1, backgroundColor: 'white' }}
                />
              )}
            </Box>

            {/* Interval Field (only show if repeatType !== NONE) */}
            {repeatType !== 'NONE' && (
              <Box>
                <Typography gutterBottom>Interval ({methods.watch('interval')})</Typography>
                <Field.FieldSlider
                  name="interval"
                  // label="Interval"
                  min={1}
                  max={intervalMaxByType[repeatType]}
                  step={1}
                />
              </Box>
            )}
          </Box>
        </DialogContent>

        <DialogActions
          sx={{
            backgroundColor: '#E7E6EA',
            borderTop: '1px solid #CDCAD5',
            px: 3,
            pb: 3,
            justifyContent: 'flex-end',
            gap: 1,
          }}
        >
          <AppButton
            variant="outlined"
            label="Cancel"
            onClick={handleClose}
            fullWidth={false}
            sx={{ backgroundColor: 'white', color: 'black' }}
            size="large"
          />
          <AppButton
            type="submit"
            variant="contained"
            label={isEdit ? 'Update' : 'Schedule Task'}
            fullWidth={false}
            isLoading={isEdit ? updateScheduledTaskMutation.isPending : scheduledTaskMutation.isPending}
            color="primary"
            size="large"
          />
        </DialogActions>
      </Form>
    </Dialog>
  );
}
