import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const ToolConfigEndPoints = {
  list: '/tools-config',
  details: '/tools-config',
};

export interface ToolConfigsType {
  toolsConfigs: {
    id: number;
    userId: number;
    toolId: number;
    name: string;
    config: any;
    createdAt: string;
    updatedAt: string;
    tool: {
      id: number;
      name: string;
      description: string;
      url: null;
      fields: null;
      icon: string;
      createdAt: string;
      updatedAt: string;
    };
    _count: {
      agentTools: number;
      teamTools: number;
    };
  }[];
  total: number;
}
// Define the Category interface

// Define the API response structure

// Create a hook to use the Agentss API
export const useToolConfigApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss with optional filters
  const useGetToolConfigs = (filters?: {
    toolId?: string;
    name?: string;
    take?: number;
    skip?: number;
  }) => {
    return apiServices.useGetListService<
      ToolConfigsType,
      { toolId?: string; name?: string; take?: number; skip?: number }
    >({
      url: ToolConfigEndPoints.list,
      params: filters,
    });
  };

  // Get a single Agents by ID
  const useGetConfig = (id: string) => {
    return apiServices.useGetItemService<any>({
      url: ToolConfigEndPoints.details,
      id,
    });
  };

  const useUpdateToolConfig = (id: string | number, onSuccess?: () => void) => {
    return apiServices.usePatchService<{ toolId: number; name: string }>({
      url: ToolConfigEndPoints.details,
      id: id?.toString(),
      onSuccess,
      queryKey: ToolConfigEndPoints.list + 'list',
    });
  };
  // Delete a template
  const useDeleteCToolConfig = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number | string>({
      url: ToolConfigEndPoints.details,
      onSuccess,
    });
  };

  return {
    useGetToolConfigs,
    useGetConfig,
    useDeleteCToolConfig,
    useUpdateToolConfig,
  };
};
