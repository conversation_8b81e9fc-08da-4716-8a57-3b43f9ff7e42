import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';

import { DashboardContent } from 'src/layouts/dashboard';
import { Iconify } from 'src/components/iconify';
import { useAuthContext, useMockedUser } from 'src/auth/hooks';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

export function OverviewView() {
  const { t } = useTranslation();
  const theme = useTheme();

  const [showAlert, setShowAlert] = useState(true);
  // const { user } = useAuthContext();
  const handleResendEmail = () => {
    // Handle resend email logic
    console.log('Resending verification email...');
  };

  const handleCloseAlert = () => {
    setShowAlert(false);
  };

  // Mock data for statistics
  const stats = [
    {
      title: 'Total Users',
      subtitle: 'We daily update these statistics',
      items: [
        { label: 'Users', value: '15,689', icon: 'mdi:account-outline', color: '#7D40D9' },
        { label: 'Managers', value: '8', icon: 'fa6-regular:chess-queen', color: '#7D40D9' },
        { label: 'Admin', value: '1', icon: 'material-symbols:diamond-outline', color: '#7D40D9' },
      ],
    },
  ];

  const metrics = [
    {
      value: '49,795',
      label: 'Operations',
      subtitle: 'Total operations by agents',
      icon: 'material-symbols:check-box-outline',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '$16,894',
      label: 'Earnings',
      subtitle: 'The platform revenue',
      icon: 'material-symbols:payments-outline',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '1,356',
      label: 'Website visits last month',
      subtitle: '',
      icon: 'material-symbols:trending-up',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
    {
      value: '92%',
      label: 'Task success completion rate',
      subtitle: '',
      icon: 'material-symbols:percent',
      color: '#7D40D9',
      bgColor: alpha('#7D40D9', 0.1),
    },
  ];

  return (
    <DashboardContent maxWidth="xl">
      {/* Header Section */}
      <Box
        component="img"
        src={
          theme.palette.mode === 'dark'
            ? '/assets/background/background-overview-dark.png'
            : '/assets/background/background-overview.png'
        }
        width="100%"
        height="100%"
      />

      {/* Verification Alert */}

      {/* Greeting */}
      {/* <Typography variant="h5" sx={{ mb: 3, fontWeight: 500 }}>
        Hello,{' '}
        <span style={{ color: '#7D40D9', fontWeight: 700, fontSize: '27px' }}>{user?.name}</span>
      </Typography> */}

      {/* Statistics Cards */}
    </DashboardContent>
  );
}
