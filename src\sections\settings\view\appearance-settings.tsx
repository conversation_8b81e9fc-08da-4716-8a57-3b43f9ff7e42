import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Stack,
  Select,
  MenuItem,
  Typography,
  FormControl,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material';
import { useColorScheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';

import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';
import { AppButton } from 'src/components/common';
import { ThemeOption } from '../component/theme-option';

// ----------------------------------------------------------------------

type Theme = 'light' | 'dark' | 'system';

const THEMES: Theme[] = ['light', 'dark', 'system'];

const LANGUAGES = [
  { value: 'english', label: 'English / UK', icon: 'emojione:flag-for-united-kingdom' },
  { value: 'arabic', label: 'Arabic / SA', icon: 'emojione:flag-for-saudi-arabia' },
];

// ----------------------------------------------------------------------

export function AppearanceSettings() {
  const settings = useSettingsContext();
  const { mode, setMode } = useColorScheme();
  const { t, i18n } = useTranslation();

  const [selectedTheme, setSelectedTheme] = useState<Theme>('system');
  const [selectedLanguage, setSelectedLanguage] = useState('english');

  const getThemeFromContext = useCallback(() => {
    return mode === 'system' ? 'system' : settings.colorScheme;
  }, [mode, settings.colorScheme]);

  const getLanguageFromContext = useCallback(() => {
    return settings.direction === 'rtl' ? 'arabic' : 'english';
  }, [settings.direction]);

  useEffect(() => {
    setSelectedTheme(getThemeFromContext());
    setSelectedLanguage(getLanguageFromContext());
  }, [getThemeFromContext, getLanguageFromContext]);

  const handleThemeChange = (theme: Theme) => {
    setSelectedTheme(theme);
  };

  const handleLanguageChange = (event: SelectChangeEvent<string>) => {
    setSelectedLanguage(event.target.value);
  };

  const handleSave = () => {
    if (selectedTheme === 'system') {
      setMode('system');
    } else {
      setMode(selectedTheme);
      settings.onUpdateField('colorScheme', selectedTheme);
    }

    const newDirection = selectedLanguage === 'arabic' ? 'rtl' : 'ltr';
    const newLang = selectedLanguage === 'arabic' ? 'ar' : 'en';
    settings.onUpdateField('direction', newDirection);
    i18n.changeLanguage(newLang);
  };

  const handleCancel = () => {
    setSelectedTheme(getThemeFromContext());
    setSelectedLanguage(getLanguageFromContext());
  };

  const isDirty =
    selectedTheme !== getThemeFromContext() || selectedLanguage !== getLanguageFromContext();

  return (
    <Stack spacing={3} alignItems="flex-start">
      <Typography variant="h3">{t('ui.appearance')}</Typography>

      <Stack spacing={1.5} sx={{ width: 1 }}>
        <Typography variant="subtitle1">{t('ui.theme')}</Typography>
        <Stack direction="row" spacing={2}>
          {THEMES.map((theme) => (
            <ThemeOption
              key={theme}
              theme={theme}
              isSelected={selectedTheme === theme}
              onClick={() => handleThemeChange(theme)}
            />
          ))}
        </Stack>
      </Stack>

      <Stack spacing={1} sx={{ width: 1, maxWidth: '100%' }}>
        <Typography variant="body1">{t('ui.language')}</Typography>
        <FormControl fullWidth>
          <Select
            value={selectedLanguage}
            onChange={handleLanguageChange}
            input={<OutlinedInput />}
            sx={{ height: '32px' }}
            renderValue={(selected) => {
              const lang = LANGUAGES.find((l) => l.value === selected);
              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Iconify icon={lang?.icon || ''} sx={{ mr: 1 }} />
                  {lang?.label}
                </Box>
              );
            }}
          >
            {LANGUAGES.map((lang) => (
              <MenuItem key={lang.value} value={lang.value}>
                <Iconify icon={lang.icon} sx={{ mr: 1 }} />
                {lang.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>

      <Stack direction="row" justifyContent="end" spacing={2} sx={{ width: 1, pt: 3 }}>
        <AppButton
          sx={{ width: '11%' }}
          color="primary"
          variant="contained"
          onClick={handleSave}
          disabled={!isDirty}
          label={t('buttons.saveChanges')}
        />
        <AppButton
          sx={{ width: '11%' }}
          variant="outlined"
          color="inherit"
          onClick={handleCancel}
          disabled={!isDirty}
          label={t('buttons.cancel')}
        />
      </Stack>
    </Stack>
  );
}
