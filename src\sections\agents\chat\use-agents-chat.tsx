import { useTheme } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { ChatEndpoints, useChatApi } from 'src/services/api/use-chat-api';
import { taskEndpoints, useTasksApi } from 'src/services/api/use-task-api';
import { authenticatedFetch } from 'src/utils/authenticated-fetch';
import { getFirstWords } from 'src/utils/truncate-text';

export type StreamedMessage = {
  id: number;
  source: 'user' | string;
  message: string;
  status: 'started' | 'processing' | 'completed';
  timestamp: string;
  event?: string | undefined;
  isError: boolean;
};

function generateTimestampId(): string {
  const now = new Date();
  const isoString = now.toISOString(); // e.g. 2025-09-18T09:31:34.438Z
  const withOffset = isoString.replace('Z', '+00:00');
  return withOffset;
}
const useAgentsChat = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const { id, agentId, cid } = useParams();
  const [openScheduleDialog, setOpenScheduleDialog] = useState(false);
  const [chatId, setChatId] = useState<number>(-1);

  const [startStreaming, setStartStreaming] = useState(false);
  const [message, setMessage] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isNewChat, setIsNewChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<StreamedMessage[]>([]);

  const [isPendingTask, setIsPendingTask] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [openConfirmationDelete, setOpenConfirmationDelete] = useState(false);
  const [chatToDelete, setChatToDelete] = useState<number | null>(null);
  const { useGetChats, useCreateChat, useDeleteChat, useUpdateChat } = useChatApi();
  const { useGetTasks, useEnhanceTask } = useTasksApi();
  const { mutate: createChat, isPending: isLoadingChat } = useCreateChat();
  const { mutate: deleteChat, isPending: isPendingDeleteChat } = useDeleteChat();
  const { mutate: enhanceTask, isPending: isPendingEnhanceChat } = useEnhanceTask(chatId || cid!);
  const { mutate: updateChat, isPending: isPendingUpdatingChat } = useUpdateChat(chatId || cid!);
  const [error, setError] = useState<any>();
  const navigate = useNavigate();
  const { state } = useLocation();
  const agentName = state?.name;
  // const { mutate: createTask, isPending: isPendingTask } = useCreateTask(cid!);
  const { data: chatResponse, isLoading: isLoadingChats } = useGetChats(agentId!);
  const { data: tasksResponse, isLoading: isLoadingTasks } = useGetTasks(chatId);

  useEffect(() => {
    setChatMessages([]);
  }, []);

  useEffect(() => {
    if (chatId === -1) setChatId(chatResponse?.chats?.[0]?.id ? chatResponse?.chats?.[0]?.id : -1);
    else setChatId(chatId);
  }, [chatResponse]);

  const handleEnhanceTask = () => {
    const data = {
      task: message,
    };
    enhanceTask(data, {
      onSuccess: (res) => {
        console.log('response :', res);
        setMessage(res?.data?.enhancedTask);
      },
    });
  };

  const handleSendMessage = async () => {
    setError('');
    setMessage('');
    // setChatMessages([]);
    setStartStreaming(true);
    if (!message || !chatId || !agentId) return;

    const firstMessage: StreamedMessage = {
      id: Math.floor(Math.random() * 10),
      source: 'user',
      message, // could be replaced with original prompt if you store it
      status: 'started',
      timestamp: `${Date?.now()}`,
      event: 'chat_message',
      isError: false,
    };
    setChatMessages((prev) => [...prev, firstMessage]);
    const url = `${import.meta.env.VITE_WORK_FORCES_SERVER_URL}${taskEndpoints.list(chatId > 0 ? chatId : cid!)}`;

    setIsPendingTask(true);
    try {
      const controller = new AbortController();
      abortControllerRef.current = controller;

      const response = await authenticatedFetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
        },
        signal: controller.signal,
        body: JSON.stringify({ content: message }),
      });

      if (!response.ok || !response.body) {
        let errorMessage = 'Something went wrong';

        try {
          // Try to parse backend error
          const errorData = await response.json();
          if (errorData?.message) {
            errorMessage = errorData.message;
          }
        } catch {
          // if parsing fails, keep default error message
        }

        setError({
          message: errorMessage,
        });
        if (response?.status === 403) {
          setTimeout(() => {
            navigate(paths.dashboard.agents.root);
          }, 3000);
        }
        if (response?.status === 400) {
          setTimeout(() => {
            navigate(paths.dashboard.agents.reconfigure(id!, agentId!), {
              state: { name: agentName },
            });
          }, 3000);
        }

        return;
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      // let isFirstEventAdded = false; // track if we inserted our synthetic first event

      const readStream = async () => {
        let currentEvent: string | undefined;

        for (;;) {
          // eslint-disable-next-line no-await-in-loop
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() ?? '';

          for (let i = 0; i < lines.length; i += 1) {
            const line = lines[i].trim();

            if (line.startsWith('event:')) {
              currentEvent = line.slice(6).trim();
            }

            if (line.startsWith('data:')) {
              const dataStr = line.slice(5).trim();
              try {
                const parsed = JSON.parse(dataStr);

                let messageEvent = '';
                const eventType = currentEvent;

                if (Array.isArray(parsed.data) && eventType?.startsWith('tool_call')) {
                  messageEvent = parsed.data
                    .map((item: any) => {
                      if (eventType === 'tool_call_request') {
                        return `🔧 Tool: ${item?.name}\nArguments:\n${item?.arguments}`;
                      }

                      if (eventType === 'tool_call_response') {
                        return `🔧 Tool: ${item?.name}\nContent:\n${item?.content}`;
                      }

                      return '';
                    })
                    .join('\n\n');
                } else {
                  messageEvent =
                    typeof parsed.data === 'string'
                      ? parsed.data
                      : parsed.data?.message ||
                        parsed.data?.[0]?.content ||
                        parsed.data?.[0]?.arguments ||
                        JSON.stringify(parsed.data);
                }

                const baseMessage: StreamedMessage = {
                  id: parsed.timestamp,
                  source:
                    parsed.data?.source || (eventType?.startsWith('tool_') ? 'Tool' : 'agent'),
                  message: messageEvent,
                  status: parsed.status,
                  timestamp: parsed.timestamp,
                  event: eventType,
                  isError: parsed.data?.is_error || false,
                };

                setChatMessages((prev) => [...prev, baseMessage]);
              } catch (err) {
                console.warn('Failed to parse stream chunk:', dataStr);
              }
            }
          }
        }
      };

      await readStream();
    } catch (error: any) {
      setIsPendingTask(false);
      if (error?.name === 'AbortError') {
        // request was cancelled by user
      } else {
        setError(error);
        console.error('Streaming error:', error);
      }
    } finally {
      setMessage('');
      setIsPendingTask(false);
      abortControllerRef.current = null;
      queryClient.invalidateQueries({
        queryKey: [taskEndpoints.list(chatId > 0 ? chatId : cid!) + 'list'],
      });
      setIsNewChat(false);

      const chatItem = chatResponse?.chats?.filter?.((item) => item?.id === chatId);
      if (chatItem?.[0]?.title === 'new chat') {
        updateChat(
          {
            title: getFirstWords(message, 4),
          },
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: [ChatEndpoints.details + 'list'],
              });
            },
          }
        );
      }
    }
  };

  const handleStopStreaming = () => {
    try {
      abortControllerRef.current?.abort();
    } catch (e) {
      // ignore
    }
    setIsPendingTask(false);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (isPendingTask) return;
      handleSendMessage();
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  const addNewChat = () => {
    const body = {
      title: 'new chat',
      agentId: +agentId!,
    };
    createChat(body, {
      onSuccess: (resChat) => {
        queryClient.resetQueries({
          queryKey: [ChatEndpoints.list + 'list'],
        });
        setChatId(resChat?.data?.id!);
        setIsNewChat(true);
        setStartStreaming(false);
        setChatMessages([]);
      },
    });
  };

  const handleDeleteChat = (chatIdToDelete: number) => {
    setChatToDelete(chatIdToDelete);
    setOpenConfirmationDelete(true);
  };

  const handleConfirmDeleteChat = () => {
    if (chatToDelete) {
      deleteChat(chatToDelete, {
        onSuccess: () => {
          setOpenConfirmationDelete(false);
          setChatToDelete(null);
          setChatId(chatResponse?.chats?.[0]?.id ? chatResponse?.chats?.[0]?.id : -1);
          setChatMessages([]);
          setStartStreaming(false);
        },
      });

      // If the deleted chat was the current one, reset to new chat state
      if (chatToDelete === chatId) {
        setChatId(-1);
        setIsNewChat(true);
      }
    }
  };

  return {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
    chatMessages,
    setChatMessages,
    startStreaming,
    setStartStreaming,
    navigate,
    chatId,
    setChatId,
    isLoadingTasks,
    error,
    addNewChat,
    isLoadingChat,
    isLoadingChats,
    agentName,
    handleDeleteChat,
    openConfirmationDelete,
    setOpenConfirmationDelete,
    handleConfirmDeleteChat,
    isPendingDeleteChat,
    handleEnhanceTask,
    handleStopStreaming,
    isPendingEnhanceChat,
    openScheduleDialog,
    setOpenScheduleDialog,
    id,
    agentId,
  };
};

export default useAgentsChat;
