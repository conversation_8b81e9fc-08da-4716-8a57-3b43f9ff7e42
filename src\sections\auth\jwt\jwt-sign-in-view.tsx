import React, { useState } from 'react';
import { z as zod } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';

import { Card, ToggleButton, ToggleButtonGroup } from '@mui/material';

import { AppButton } from 'src/components/common';
import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { useAuthContext } from 'src/auth/hooks';
import { signInWithPassword } from 'src/auth/context/jwt';
import { CONFIG } from 'src/config-global';

import { OAUTH_CLIENT_ID, OAUTH_REDIRECT_URL, OAUTH_SERVER_URL } from './constants';
import MisrajIcon from './components/misraj-icon';

// ----------------------------------------------------------------------

export const SignInSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  password: zod
    .string()
    .min(1, { message: 'Password is required!' })
    .min(6, { message: 'Password must be at least 6 characters!' }),
});

export type SignInSchemaType = zod.infer<typeof SignInSchema>;

// ----------------------------------------------------------------------

export function JwtSignInView() {
  const { t } = useTranslation();
  const router = useRouter();
  const { checkUserSession } = useAuthContext();
  const [errorMsg, setErrorMsg] = useState('');
  const [activeTab, setActiveTab] = useState('login');
  const [showPassword, setShowPassword] = useState(false);
  const handleChange = (_event: React.MouseEvent<HTMLElement>, newAlignment: string) => {
    if (newAlignment !== null) {
      setActiveTab(newAlignment);
      if (newAlignment === 'create') {
        router.push(paths.auth.jwt.signUp); // Navigate to create account page
      }
    }
  };

  const methods = useForm<SignInSchemaType>({
    resolver: zodResolver(SignInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const handleAdminLogin = () => {
    const state = Math.random().toString(16).slice(2);
    localStorage.setItem('oauth_state', state);
    window.location.href = `${OAUTH_SERVER_URL}/oauth2/auth?client_id=${OAUTH_CLIENT_ID}&scope=openid offline&response_type=code&redirect_uri=${OAUTH_REDIRECT_URL}&state=${state}`;
  };
  const onSubmit = handleSubmit(async (data) => {
    try {
      await signInWithPassword({
        email: data.email,
        password: data.password,
      });
      await checkUserSession?.();

      // Get returnTo parameter or default to dashboard
      const searchParams = new URLSearchParams(window.location.search);
      const returnTo = searchParams.get('returnTo') || paths.dashboard.root;

      router.push(returnTo);
    } catch (error) {
      console.error('Sign in error:', error);
      setErrorMsg(error?.message);
    }
  });

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: 456,

        mx: 'auto',
        my: '16px',
        bgcolor: 'rgba(244, 244, 244)',
        p: 4,
        borderRadius: 2,
        boxShadow: 3,
      }}
    >
      <Box justifyContent="center" sx={{ mb: 1 }} display="flex">
        <Box
          component="img"
          src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
          sx={{ width: 'auto', height: 44 }}
        />
        <Typography fontWeight="bold" variant="h3" p="6px" lineHeight="32px">
          {t('auth.signInView.workforces')}
        </Typography>
      </Box>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 2,
          mb: 0,
          borderRadius: '8px',
          height: '38px',
          backgroundColor: 'rgba(235, 234 ,238)',
          p: 0,
        }}
      >
        <ToggleButtonGroup
          value={activeTab}
          exclusive
          fullWidth
          onChange={handleChange}
          aria-label="Login or Create Account"
          sx={{
            width: '100%',
            gap: 0,
            p: 0.1,
            m: 0,
          }}
        >
          <ToggleButton
            value="login"
            sx={{
              p: 0,
              m: 0,
              flex: 1,
              minWidth: 0,
              borderRadius: 0,
              color: activeTab === 'login' ? 'rgba(15, 14, 17, 1)' : 'text.disabled',
              backgroundColor: activeTab === 'login' ? 'common.white' : 'rgba(235, 234, 238, 1)',
              '&.Mui-selected': {
                backgroundColor: 'common.white',
                color: 'rgba(15, 14, 17, 1)',
                '&:hover': {
                  backgroundColor: 'white',
                },
              },
            }}
          >
            {t('auth.signInView.loginTab')}
          </ToggleButton>

          <ToggleButton
            value="create"
            sx={{
              p: 0,
              m: 0,
              flex: 1,
              minWidth: 0,
              borderRadius: 0,
              color: activeTab === 'create' ? 'rgba(15, 14, 17, 1)' : 'text.disabled',
              backgroundColor: activeTab === 'create' ? 'common.white' : 'rgba(235, 234, 238, 1)',
              '&.Mui-selected': {
                backgroundColor: 'common.white',
                color: 'rgba(15, 14, 17, 1)',
                '&:hover': {
                  backgroundColor: 'grey.200',
                },
              },
              '&:hover': {
                backgroundColor: activeTab === 'create' ? 'grey.200' : 'grey.300',
              },
            }}
          >
            {t('auth.signInView.createAccountTab')}
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Typography
        variant="body1"
        fontWeight={400}
        textAlign="start"
        sx={{ my: 3, fontSize: '1.1rem', color: 'rgba(15, 14, 17, 1)' }}
      >
        {t('auth.signInView.subtitle')}
      </Typography>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}

      <Form methods={methods} onSubmit={onSubmit}>
        <Stack spacing={3}>
          <Card
            sx={{
              border: '1px solid rgba(224, 223, 226, 1)',
              borderRadius: '12px',
              p: 2,
              backgroundColor: 'white',
              display: 'flex',
              flexDirection: 'column',
              height: 'fit-content',
              width: '100%',
            }}
          >
            {/* Email Field */}

            <Field.Text
              name="email"
              label={t('auth.signInView.emailLabel')}
              placeholder={t('auth.signInView.emailPlaceholder')}
              fullWidth
              variant="standard"
              InputProps={{
                disableUnderline: true,
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify
                      icon="mdi:mail-outline"
                      width={28}
                      sx={{
                        mr: 2,
                        transform: 'translateY(-15px)',
                        color: 'rgba(144, 108, 229, 1)',
                      }}
                    />
                  </InputAdornment>
                ),
                sx: {
                  px: 1,

                  height: 35,
                  borderRadius: 2,
                },
              }}
              InputLabelProps={{
                style: {
                  fontSize: '1.1rem',
                  color: 'rgba(70, 70, 70, 1)',
                },
                sx: {
                  color: 'red',
                  px: 10,
                },
                shrink: true,
              }}
            />

            <Divider sx={{ mb: 2 }} />

            <Field.Text
              name="password"
              label={t('auth.signInView.passwordLabel')}
              placeholder="••••••••••••••••••••••"
              type={showPassword ? 'text' : 'password'}
              fullWidth
              variant="standard"
              InputProps={{
                disableUnderline: true,
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify
                      icon="mdi:key-outline"
                      width={28}
                      sx={{
                        mr: 2,
                        transform: 'translateY(-15px)',
                        color: 'rgba(144, 108, 229, 1)',
                      }}
                    />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton edge="end" onClick={() => setShowPassword(!showPassword)}>
                      <Iconify
                        icon={showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                        width={24}
                        sx={{ transform: 'translateY(-15px)', color: 'rgba(144, 108, 229, 1)' }}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  px: 1,
                  height: 35,
                  borderRadius: 2,
                },
              }}
              InputLabelProps={{
                style: {
                  fontSize: '1.1rem',
                  color: 'rgba(70, 70, 70, 1)',
                },
                sx: {
                  px: 10,
                },
                shrink: true,
              }}
            />
          </Card>

          <Link
            component={RouterLink}
            href={paths.auth.jwt.forgotPassword}
            variant="body2"
            color="inherit"
            sx={{ alignSelf: 'flex-end', display: 'flex' }}
          >
            <Iconify
              icon="mdi:help-circle-outline"
              width={22}
              sx={{ mx: 1, color: 'primary.main' }}
            />
            <Typography color="primary.main"> {t('auth.signInView.forgotPassword')}</Typography>
          </Link>

          <AppButton
            label={t('auth.signInView.loginButton')}
            fullWidth
            color="primary"
            size="large"
            type="submit"
            variant="contained"
            isLoading={isSubmitting}
            sx={{
              backgroundColor: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.main',
                boxShadow: '0px 5px 5px rgba(0, 0, 0, 0.2)', // Added box shadow on hover
              },
            }}
          />

          <Stack direction="row" alignItems="center" spacing={2} sx={{}}>
            <Divider sx={{ flexGrow: 1 }}>{t('auth.signInView.orDivider')}</Divider>
          </Stack>
          <Stack spacing={1}>
            <AppButton
              fullWidth
              variant="outlined"
              onClick={handleAdminLogin}
              color="inherit"
              label={t('auth.signInView.continueWithMisraj')}
              sx={{ bgcolor: 'rgba(255, 255, 255, 0.7)', height: '44px' }}
              startIcon={<MisrajIcon />}
            />
          </Stack>
        </Stack>
      </Form>
    </Box>
  );
}
