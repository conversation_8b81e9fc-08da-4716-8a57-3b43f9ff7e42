import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/config-global';
import { usePermissions } from 'src/auth/hooks/use-permissions';

import { SvgColor } from 'src/components/svg-color';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function useNavData() {
  const { t } = useTranslation();
  const {
    canAccessAgents,
    canAccessTemplates,
    canAccessTeams,
    canAccessTasks,
    canAccessChats,
    canManageCategories,
    hasPermission,
  } = usePermissions();
  const icon = (name: string) => (
    <SvgColor src={`${CONFIG.site.basePath}/assets/icons/navbar/${name}.svg`} />
  );

  const ICONS = {
    job: icon('ic-job'),
    blog: icon('ic-blog'),
    chat: icon('ic-chat'),
    mail: icon('ic-mail'),
    user: icon('ic-user'),
    file: icon('ic-file'),
    lock: icon('ic-lock'),
    tour: icon('ic-tour'),
    order: icon('ic-order'),
    label: icon('ic-label'),
    blank: icon('ic-blank'),
    kanban: icon('ic-kanban'),
    folder: icon('ic-folder'),
    course: icon('ic-course'),
    banking: icon('ic-banking'),
    booking: icon('ic-booking'),
    invoice: icon('ic-invoice'),
    product: icon('ic-product'),
    calendar: icon('ic-calendar'),
    disabled: icon('ic-disabled'),
    external: icon('ic-external'),
    menuItem: icon('ic-menu-item'),
    ecommerce: icon('ic-ecommerce'),
    analytics: icon('ic-analytics'),
    dashboard: icon('ic-dashboard'),
    parameter: icon('ic-parameter'),
  };

  // ----------------------------------------------------------------------

  const arrayOfRoutes = [
    /**
     * Overview
     */
    {
      subheader: ' ',
      items: [
        {
          title: t('pages.dashboard.overView'),
          path: paths.dashboard.root,
          icon: <Iconify icon="icon-park-outline:windows" />,
        },

        // Agents section - only show if user has agent permissions
        ...(canAccessAgents()
          ? [
              {
                title: t('components.navigation.agents'),
                path: paths.dashboard.agents.root,
                icon: <Iconify icon="material-symbols-light:robot-2-outline-sharp" />,
                permissions: ['get-agents', 'get-agent'],
                children: [
                  ...(canAccessTemplates()
                    ? [
                        {
                          title: t('components.navigation.templates'),
                          path: paths.dashboard.agents.templates,
                          permissions: ['get-templates'],
                        },
                      ]
                    : []),
                  {
                    title: t('pages.agents.myAgents'),
                    path: paths.dashboard.agents.agents,
                    permissions: ['get-agents'],
                  },
                ],
              },
            ]
          : []),

        // Teams section - only show if user has team permissions
        ...(canAccessTeams()
          ? [
              {
                title: t('components.navigation.teams'),
                path: paths.dashboard.teams.root,
                icon: <Iconify icon="mdi:account-group-outline" />,
                permissions: ['get-templates-teams'],
                children: [
                  {
                    title: t('components.navigation.templates'),
                    path: paths.dashboard.teams.templates,
                    permissions: ['get-templates-teams'],
                  },
                  {
                    title: t('components.navigation.myTemplates'),
                    path: paths.dashboard.teams.my_team_templates,
                    permissions: ['get-templates-team'],
                  },
                ],
              },
            ]
          : []),

        // Knowledge Base - show for all authenticated users
        {
          title: t('components.navigation.connectionsIntegrations'),
          path: paths.dashboard.knowledgeBase,
          icon: <Iconify icon="icon-park-outline:triangle-round-rectangle" />,
        },

        // Settings - show for all authenticated users
        {
          title: t('components.navigation.settings'),
          path: paths.dashboard.settings,
          icon: <Iconify icon="fa6-regular:chess-queen" />,
        },

        // Scheduled Tasks - only show if user has task permissions
        ...(canAccessTasks()
          ? [
              {
                title: t('components.navigation.scheduledTasks'),
                path: paths.dashboard.scheduled.root,
                icon: <Iconify icon="mdi:invoice-text-scheduled-outline" />,
                permissions: ['get-tasks'],
              },
            ]
          : []),
      ].filter(Boolean), // Remove any undefined items
    },
    /**
     * Management
     */
    // {
    //   subheader: 'Management',
    //   items: [
    //     {
    //       title: 'Group',
    //       path: paths.dashboard.group.root,
    //       icon: ICONS.user,
    //       children: [
    //         { title: 'Four', path: paths.dashboard.group.root },
    //         { title: 'Five', path: paths.dashboard.group.five },
    //         { title: 'Six', path: paths.dashboard.group.six },
    //       ],
    //     },
    //   ],
    // },
  ];

  const dashboardNavData = useMemo(() => arrayOfRoutes, [t]);

  return dashboardNavData;
}
