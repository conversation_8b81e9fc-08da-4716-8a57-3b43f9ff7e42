import { paths } from 'src/routes/paths';

import axios from 'src/utils/axios';

// ----------------------------------------------------------------------

export async function setSession(accessToken: string | null) {
  try {
    if (accessToken) {
      axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
    } else {
      delete axios.defaults.headers.common.Authorization;
    }
  } catch (error) {
    console.error('Error during set session:', error);
    delete axios.defaults.headers.common.Authorization;
    throw error;
  }
}

// ----------------------------------------------------------------------

/**
 * Clear authentication data
 */
export function clearAuthData(): void {
  try {
    delete axios.defaults.headers.common.Authorization;
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
}

// ----------------------------------------------------------------------

/**
 * Navigate to sign in page
 */
export function navigateToSignIn(): void {
  try {
    clearAuthData();
    window.location.href = paths.auth.jwt.signIn;
  } catch (error) {
    console.error('Error navigating to sign in:', error);
  }
}
