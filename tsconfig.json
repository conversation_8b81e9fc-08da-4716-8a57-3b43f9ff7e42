{"compilerOptions": {"baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "strict": true, "noEmit": true, "allowJs": true, "module": "esnext", "jsx": "react-jsx", "skipLibCheck": true, "noImplicitAny": true, "noImplicitThis": true, "esModuleInterop": true, "isolatedModules": true, "strictNullChecks": true, "resolveJsonModule": true, "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "useUnknownInCatchVariables": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true}, "include": ["**/*.tsx", "src/**/*"], "exclude": ["node_modules"], "references": [{"path": "./tsconfig.node.json"}]}