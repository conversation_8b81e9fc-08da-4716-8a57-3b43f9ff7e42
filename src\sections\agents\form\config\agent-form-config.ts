import { MODEL_VALUES } from 'src/constants/constants';
import { z } from 'zod';

// ----------------------------------------------------------------------

// Form validation schema - Note: These will be translated at runtime
export const agentFormSchema = z
  .object({
    name: z.string().min(1, 'validation.nameRequired').max(100, 'validation.nameMaxLength'),
    description: z
      .string()
      .min(1, 'validation.descriptionRequired')
      .max(500, 'validation.descriptionMaxLength'),
    systemMessage: z
      .string()
      .min(1, 'validation.systemMessageRequired')
      .min(10, 'validation.systemMessageMinLength'),
    type: z.enum(['SINGLE', 'TEAM'], {
      required_error: 'validation.typeRequired',
    }),
    status: z.enum(['ACTIVE', 'DISABLED'], {
      required_error: 'validation.statusRequired',
    }),
    category: z.string().min(1, 'validation.categoryRequired'),
    toolsId: z.array(z.number()).optional(),
    model: z.enum(MODEL_VALUES, {
      required_error: 'validation.modelRequired',
    }),

    categoryId: z.coerce.number().optional(),
  })
  .transform((data) => ({
    ...data,
    categoryId: Number(data.category),
  }));

// Form values type
export type AgentFormValues = z.infer<typeof agentFormSchema>;

// Agent type options
export const AGENT_TYPE_OPTIONS = [
  {
    value: 'SINGLE',
    label: 'Single Agent',
    description: 'A standalone agent that works independently',
    icon: 'mdi:account-outline',
  },
  {
    value: 'TEAM',
    label: 'Multi Agent',
    description: 'An agent that works as part of a team',
    icon: 'mdi:account-group-outline',
  },
] as const;

// Original format for compatibility
export const TYPE_OPTIONS = AGENT_TYPE_OPTIONS.map((option) => ({
  value: option.value,
  label: option.label,
}));

// Agent status options
export const AGENT_STATUS_OPTIONS = [
  {
    value: 'ACTIVE',
    label: 'Active',
    description: 'Agent is active and available for use',
    color: 'success' as const,
  },
  {
    value: 'DISABLED',
    label: 'Disabled',
    description: 'Agent is disabled and not available for use',
    color: 'error' as const,
  },
] as const;

// Original format for compatibility
export const STATUS_OPTIONS = AGENT_STATUS_OPTIONS.map((option) => ({
  value: option.value,
  label: option.label,
}));

// LLM Model options

// Form steps configuration
export const FORM_STEPS = [
  {
    id: 'details',
    label: 'Agent Details',
    description: 'Basic information about your agent',
    icon: 'mdi:information-outline',
    fields: ['name', 'description', 'systemMessage', 'type', 'status'] as const,
  },
  {
    id: 'category',
    label: 'Category',
    description: 'Select the category for your agent',
    icon: 'mdi:tag-outline',
    fields: ['category'] as const,
  },
  {
    id: 'tools',
    label: 'Tools',
    description: 'Choose tools for your agent',
    icon: 'mdi:tools',
    fields: ['category'] as const,
    // fields: ['toolsId'] as const,
  },
  {
    id: 'model',
    label: 'LLM MODEL',
    description: 'Select the AI model to use',
    icon: 'mdi:brain',
    fields: ['model'] as const,
  },
] as const;

// Default form values
export const DEFAULT_FORM_VALUES: Partial<AgentFormValues> = {
  name: '',
  description: '',
  systemMessage: '',

  type: 'SINGLE',
  status: 'ACTIVE',
  category: '',
  toolsId: [],
  model: 'GPT_4O_MINI',
};

// Form field configurations - These keys will be used for translation
export const FORM_FIELD_CONFIG = {
  name: {
    label: 'forms.agentName',
    placeholder: 'forms.agentNamePlaceholder',
    helperText: 'forms.agentNameHelper',
  },
  description: {
    label: 'forms.description',
    placeholder: 'forms.descriptionPlaceholder',
    helperText: 'forms.descriptionHelper',
    multiline: true,
    rows: 3,
  },
  systemMessage: {
    label: 'forms.systemInstructions',
    placeholder: 'forms.systemInstructionsPlaceholder',
    helperText: 'forms.systemInstructionsHelper',
    multiline: true,
    rows: 4,
  },
} as const;
