export const en = {
  pages: {
    dashboard: {
      title: 'Dashboard',
      teams: 'Teams',
      agents: 'Agents',
      categories: 'Categories',
      managers: 'Managers',
      createManager: 'Create Manager',
      editManager: 'Edit Manager',
      preferences: 'Preferences',
      pageOne: 'Page One',
      overView: 'Overview',
    },
    profile: {
      pageTitle: 'Profile',
      knowledgeBase: 'Knowledge Base',
      settings: 'Settings',
    },
    auth: {
      signIn: 'Sign in',
      signUp: 'Sign up',
      resetPassword: 'Reset Password',
      newPassword: 'New Password',
    },
    error: {
      notFoundTitle: '404 page not found!',
    },
    scheduledTasks: {
      title: 'Scheduled Tasks',
      description: 'You can check your scheduled tasks and show the results from this table',
      noTasksFound: 'No scheduled tasks found',
    },
    connectionsIntegrations: {
      title: 'Connections & Integrations',
      description: 'Manage your connections and integrations',
      knowledgeBase: 'Knowledge Base',
      configTools: 'Config Tools',
      cloudPlatforms: 'Cloud Platforms',
      databases: 'Databases',
      apis: 'APIs',
      uploadFiles: 'Upload Files',
      connectResource: 'Connect Resource',
      searchFiles: 'Search files...',
      filterByDate: 'Filter by date',
      filterByType: 'Filter by type',
      allTypes: 'All types',
      document: 'Document',
      image: 'Image',
      video: 'Video',
      audio: 'Audio',
      other: 'Other',
    },
    agents: {
      templates: 'Templates',
      myAgents: 'My Agents',
      searchTemplates: 'Search templates...',
      allCategories: 'All Categories',
      cardView: 'Card View',
      tableView: 'Table View',
      createNewTemplate: 'Create New Template',
    },
    teams: {
      templates: 'Templates',
      teamsTemplates: "Teams' Templates",
      myTemplates: 'My Templates',
      myTeamsTemplate: 'My Team’s Templates',
      searchTemplates: 'Search templates...',
      allCategories: 'All Categories',
      cardView: 'Card View',
      tableView: 'Table View',
      createNewTeam: 'Create New Team',
      description: 'Enable advanced workflows with applications',
    },
  },
  auth: {
    welcome: 'Welcome to Midad!',
    welcomeSubtitle:
      'Access your personalized dashboard, track your workflows, and manage your tasks.',
    joinUs: 'Join us and start automating your workflows!',
    joinUsSubtitle:
      'Sign up today to gain access to our platform, manage automations, and unlock the power of seamless task automation.',
    resetPassword: 'Reset your password',
    resetPasswordSubtitle: 'Enter your email and we will send you a link to reset your password.',
    setNewPassword: 'Set new password',
    setNewPasswordSubtitle: 'Create a new password for your account.',
    newPasswordView: {
      title: 'Set New Password',
      subtitle: "Enter your email, and we'll send you a link to reset your password.",
      newPassword: 'New Password',
      newPasswordPlaceholder: 'Enter your new password',
      confirmPassword: 'Confirm Password',
      confirmPasswordPlaceholder: 'Confirm your new password',
      changePassword: 'Change Password',
      passwordRequirements: {
        minLength: 'Minimum 8 characters long',
        hasUpperLower: 'Must include uppercase, lowercase, and a number',
        hasSpecial: 'Must contain at least one special character',
        noSpaces: 'No spaces before or after the password',
      },
      footerLinks: {
        privacyPolicy: 'Privacy policy',
        termsOfUse: 'Terms of use',
        dmca: 'DMCA',
      },
    },
    forgotPassword: 'Forgot password?',
    rememberPassword: 'Remember password?',
    login: 'Login',
    register: 'Register',
    alreadyHaveAccount: 'Already have an account?',
    orRegisterWith: 'Or register with',
    orLoginWith: 'Or login with',
    email: 'Email',
    password: 'Password',
    name: 'Name',
    enterYourEmail: 'Enter your email',
    enterYourPassword: 'Enter your password',
    enterYourName: 'Enter your name',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    enterYourNewPassword: 'Enter your new password',
    confirmYourNewPassword: 'Confirm your new password',
    changePassword: 'Change Password',
    send: 'Send',
    passwordResetSuccess: 'Password changed successfully!',
    passwordValidation: {
      minLength: 'Must be at least 8 characters',
      hasUpperLower: 'Must include uppercase and lowercase letters',
      hasNumber: 'Must include at least one number',
      hasSpecial: 'Must include at least one special character',
      noSpaces: 'Must not have spaces at beginning or end',
      passwordsMatch: 'Passwords must match',
    },
    codeSent: "We've sent a 5-digit code to your email {email}.",
    emailConfirmation: 'Email confirmation',
    emailConfirmationMessage: 'We have sent you an email to confirm your registration process',
    resetPasswordView: {
      title: 'Reset password',
      subtitle: "Enter your email, and we'll send you a link to reset your password.",
      emailLabel: 'Email',
      emailPlaceholder: 'Enter your email',
      sendButton: 'Send',
      rememberPassword: 'Remember password?',
      loginLink: 'Login',
      codeSentMessage: "We've sent a 5-digit code to your email {email}",
      enterCodeMessage: 'Please enter it below.',
      checkingButton: 'Checking...',
      checking: 'Checking...',
      codeVerified: 'Code verified successfully! Redirecting to password change page...',
      footerLinks: {
        privacyPolicy: 'Privacy policy',
        termsOfUse: 'Terms of use',
        dmca: 'DMCA',
      },
    },
  },
  error: {
    notFound: 'Sorry, page not found!',
    notFoundDescription:
      "Sorry, we couldn't find the page you're looking for. Perhaps you've mistyped the URL? Be sure to check your spelling.",
    forbidden: 'No permission',
    forbiddenDescription:
      "The page you're trying to access has restricted access. Please refer to your system administrator.",
    serverError: '500 Internal server error',
    serverErrorDescription: 'There was an error, please try again later.',
    permissionDenied: 'Permission denied',
    permissionDeniedDescription: 'You do not have permission to access this page.',
    goToHome: 'Go to home',
  },
  components: {
    searchNotFound: {
      enterKeywords: 'Please enter keywords',
      notFound: 'Not found',
      noResults: 'No results found for',
      checkTypos: 'Try checking for typos or using complete words.',
    },
    chat: {
      chatsHistory: 'Chats History',
      newChat: 'New Chat',
      back: 'back',
      generatingResponse: 'generating response....',
      askAnything: 'Ask me anything',
      enhancePrompt: 'enhance your prompt',
      scheduleTask: 'Schedule your task',
      stopGeneration: 'stop generation',
      sendTask: 'send task',
      deleteChat: 'Delete Chat',
      deleteChatConfirm: 'Are you sure you want to delete this chat? This action cannot be undone.',
      cancel: 'Cancel',
      delete: 'Delete',
    },
    agents: {
      model: 'Model:',
      specialRequest: 'Special Request:',
      useAgent: 'Use Agent',
      failedToLoad: 'Failed to load agents. Please try again.',
      retry: 'Retry',
      myAgents: 'My Agents',
      enableWorkflows: 'Enable advanced workflows with applications',
      createNewAgent: 'Create New Agent',
      searchAgents: 'Search agents...',
      loadingMoreAgents: 'Loading more agents...',
      reConfigure: 'Re Configure',
      duplicate: 'Duplicate',
      delete: 'Delete',
      noAgentsFound: 'No agents found',
      adjustSearch:
        "Try adjusting your search or filter criteria to find the agents you're looking for.",
      deleteAgent: 'Delete agent',
      deleteAgentConfirm:
        'Are you sure you want to delete this agent? This action cannot be undone.',
    },
    scheduledTasks: {
      taskRequired: 'Task is required',
      repeatTypeRequired: 'Repeat type is required',
      startDateRequired: 'Start date is required',
      intervalMin: 'Interval must be at least 1',
      endDateRequired: 'End date is required when repeat type is not None',
      endDateGreater: 'End date must be greater than start date',
      repeatTypes: {
        none: 'None',
        hourly: 'Hourly',
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
      },
      updateScheduledTask: 'Update Scheduled Task',
      scheduleTask: 'Schedule Task',
      modifySchedule: 'Modify the recurring task schedule',
      setupSchedule: 'Set up a recurring task schedule',
      repeatType: 'Repeat Type',
      startDate: 'Start Date',
      endDate: 'End Date',
      interval: 'Interval',
      update: 'Update',
    },
    noData: {
      noDataAvailable: 'No data available',
      listAvailableMessage: "You'll see {item} listed here once they're available",
    },
    notifications: {
      title: 'Notifications',
      markAllAsRead: 'Mark all as read',
      viewAll: 'View all',
      markAsRead: 'Mark as read',
      today: 'Today',
      yesterday: 'Yesterday',
      older: 'Older',
      noNotifications: 'No notifications',
      noNotificationsDescription:
        "You'll receive notifications for important updates and activity.",
    },
    accountMenu: {
      profile: 'Profile',
      dashboard: 'Dashboard',
      settings: 'Settings',
      logout: 'Logout',
    },
    dialogs: {
      confirmDeleteMessage: 'Are you sure?',
      deleteTeam: 'Delete Team?',
      deleteTeamConfirm: 'Are you sure you want to delete this team?',
      logout: 'Logout',
      logoutConfirm: 'Are you sure you want to logout?',
    },
    buttons: {
      delete: 'Delete',
      cancel: 'Cancel',
      save: 'Save',
      next: 'Next',
      previous: 'Previous',
      create: 'Create',
      update: 'Update',
      createNewTeam: 'Create New Team',
      clearAll: 'Clear All',
      saveChanges: 'Save Changes',
      uploadNewImage: 'Upload New Image',
    },
    tables: {
      rowsPerPageLabel: 'Rows per page',
      dense: 'Dense',
    },
    common: {
      successResult: 'Completed successfully',
      view: 'View',
      edit: 'Edit',
      search: 'Search...',
      searchFiles: 'Search files...',
      searchServices: 'Search services...',
      member: 'member',
      members: 'members',
      created: 'Created',
      privacyPolicy: 'Privacy policy',
      termsOfUse: 'Terms of use',
      dmca: 'DMCA',
    },
    dashboard: {
      overView: 'Overview',
      categories: 'Categories',
      teams: 'Teams',
    },
    user: {
      title: 'Hello',
    },
    header: {
      workForces: 'workForces',
    },
    navigation: {
      overview: 'Overview',
      categories: 'Categories',
      agents: 'Agents',
      teams: 'Teams',
      managers: 'Managers',
      users: 'Users',
      settings: 'Settings',
      home: 'Home',
      projects: 'Projects',
      subscription: 'Subscription',
      security: 'Security',
      accountSettings: 'Account settings',
      templates: 'Templates',
      myTemplates: 'My Templates',
      connectionsIntegrations: 'Connections & Integrations',
      scheduledTasks: 'Scheduled Tasks',
    },
    search: {
      placeholder: 'Search...',
      searchFiles: 'Search files...',
      searchServices: 'Search services...',
    },
    workspaces: {
      free: 'Free',
      pro: 'Pro',
      team1: 'Team 1',
      team2: 'Team 2',
      team3: 'Team 3',
    },
    profile: {
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      username: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      keepCurrentPassword: 'Leave blank to keep current password',
      profileUpdated: 'Profile updated successfully!',
      settings: {
        language: 'Language',
        chooseLanguage: 'Choose your preferred language',
        managePreferences: 'Manage your account settings and preferences',
        theme: 'Theme',
        chooseTheme: 'Choose your preferred theme',
        light: 'Light',
        dark: 'Dark',
        system: 'System',
        languageOptions: {
          english: 'English',
          arabic: 'Arabic',
        },
      },
      validation: {
        firstNameRequired: 'First name is required',
        lastNameRequired: 'Last name is required',
        emailValid: 'Email must be a valid email address',
        usernameLength: 'Username must be at least 3 characters',
        passwordLength: 'Password must be at least 8 characters',
        passwordUppercase: 'Password must contain at least one uppercase letter',
        passwordLowercase: 'Password must contain at least one lowercase letter',
        passwordNumber: 'Password must contain at least one number',
        passwordsMatch: "Passwords don't match",
      },
    },
    teams: {
      title: 'Teams',
      chooseAiModel: 'Choose AI model for your team',
      searchModels: 'Search models...',
      form: {
        editTeam: 'Edit Team',
        createNewTeam: 'Create New Team',
        formInstructions: 'Please fill out the required fields in each steps',
        steps: {
          teamInfo: 'Team Info',
          resources: 'Resources',
          aiModel: 'AI Model',
          members: 'Members',
          instructions: 'Instructions',
        },
        fields: {
          teamName: 'Team Name',
          description: 'Description',
          instructions: 'Instructions',
        },
      },
      resources: {
        searchFolders: 'Search folders...',
        searchFiles: 'Search files in this folder...',
        searchAiModels: 'Search AI models...',
        searchMembers: 'Search members...',
        selectedFiles: 'Selected Files',
        chooseAiModel: 'Choose the AI model',
        chooseTeamMembers: 'Choose the team members',
      },
    },
  },
  validation: {
    required: '{field} is required',
    nameRequired: 'Name is required',
    descriptionRequired: 'Description is required',
    systemMessageRequired: 'System message is required',
    typeRequired: 'Type is required',
    statusRequired: 'Status is required',
    categoryRequired: 'Category is required',
    modelRequired: 'Model is required',
    categoryNameRequired: 'Category name is required',
    nameMaxLength: 'Name must be less than 100 characters',
    descriptionMaxLength: 'Description must be less than 500 characters',
    systemMessageMinLength: 'System message must be longer than or equal to 10 characters',
    atLeastOneTemplate: 'At least one template is required',
    emailRequired: 'Email is required!',
    emailValid: 'Email must be a valid email address!',
    passwordRequired: 'Password is required!',
    passwordMinLength: 'Password must be at least 6 characters!',
    usernameRequired: 'Username is required!',
    nameRequiredExclamation: 'Name is required!',
  },
  forms: {
    agentName: 'Agent Name',
    agentNamePlaceholder: 'Enter a descriptive name for your agent',
    agentNameHelper: "Choose a clear, descriptive name that reflects the agent's purpose",
    description: 'Description',
    descriptionPlaceholder: 'Describe what your agent does and its main capabilities',
    descriptionHelper: "Provide a clear description of the agent's purpose and functionality",
    systemInstructions: 'System Instructions',
    systemInstructionsPlaceholder:
      'Enter detailed instructions that define how the agent should behave and respond',
    systemInstructionsHelper: "These instructions guide the agent's behavior and responses",
    agentDescriptionPlaceholder: 'Describe what you want from this agent...',
    createTemplateTooltip: 'create template configure by AI',
    generate: 'Generate',
    search: 'Search',
    searchPlaceholder: 'Search...',
    type: 'Type',
    category: 'Category',
    tools: 'Tools',
    model: 'Model',
    status: 'Status',
  },
  loading: {
    failedToLoadTemplates: 'Failed to load templates. Please try again.',
    retry: 'Retry',
    loading: 'Loading...',
    checking: 'Checking...',
    generating: 'Generating...',
  },
  ui: {
    workforces: 'Workforces',
    blank: 'Blank',
    hello: 'Hello',
    profile: 'Profile',
    password: 'Password',
    appearance: 'Appearance',
    deleteAccount: 'Delete Account',
    theme: 'Theme',
    language: 'Language',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    profielPicture: 'Profile Picture',
    firstName: 'First Name',
    lastName: 'Last Name',
    userName: 'User Name',
    email: 'Email',
    developerMode: 'Developer Mode',
  },
  settings: {
    title: 'Settings',
    logout: 'Logout?',
    reset: 'Reset',
    close: 'Close',
    darkMode: 'Dark mode',
    contrast: 'Contrast',
    rtl: 'Right to left',
    compact: 'Compact',
    compactTooltip: 'Dashboard only and available at large resolutions > 1600px (xl)',
    nav: 'Nav',
    navTooltip: 'Dashboard only',
    layout: 'Layout',
    color: 'Color',
    font: 'Font',
    presets: 'Presets',
    exit: 'Exit',
    fullScreen: 'Full Screen',
  },
  categories: {
    title: 'Categories',
    dashboard: 'Dashboard',
    createNew: 'Create New Category',
    noCategories: 'No categories found',
    createFirst: 'Create your first category to get started',
    tryAgain: 'Please try again later',
  },
  table: {
    name: 'Name',
    dateCreated: 'Date Created',
    type: 'Type',
    category: 'Category',
    tools: 'Tools',
    llmModel: 'LLM Model',
    status: 'Status',
    action: 'Action',
    actions: 'Actions',
    fileName: 'File Name',
    uploadDate: 'Upload Date',
    size: 'Size',
    usersCount: 'Users Count',
    tool: 'Tool',
    agentsCount: 'Agents Count',
    taskContent: 'Task Content',
    repeatType: 'Repeat Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    nextRun: 'Next Run',
    lastRun: 'Last Run',
    interval: 'Interval',
    taskStatus: 'Task Status',
    publishRequestStatus: 'Publish request status',
  },
  buttons: {
    edit: 'Edit',
    clone: 'Clone',
    delete: 'Delete',
    publish: 'Publish',
    view: 'View',
    create: 'Create',
    update: 'Update',
    save: 'Save',
    cancel: 'Cancel',
    createNew: 'Create New',
    updateTemplate: 'Update Template',
    createTemplate: 'Create Template',
    scheduleTask: 'Schedule Task',
    updateScheduledTask: 'Update Scheduled Task',
    saveChanges: 'Save Changes',
    deleteAccount: 'Delete Account',
    changePassword: 'Change Password',
    updateProfile: 'Update Profile',
    upgrade: 'Upgrade',
    uploadNewImage: 'Upload New Image',
  },
  status: {
    active: 'Active',
    inactive: 'Inactive',
    disabled: 'Disabled',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    running: 'Running',
    success: 'Success',
    error: 'Error',
    paused: 'Paused',
  },
  messages: {
    toolRequest: 'Tool Request',
    toolResponse: 'Tool Response',
    tool: 'Tool',
    taskResult: 'Task Result',
    taskCompleted: 'Task Completed',
    taskFailed: 'Task Failed',
    somethingWentWrong: 'Something went wrong',
    taskCompletedSuccessfully: 'Task completed',
    anErrorOccurred: 'An error occurred',
  },
  filters: {
    all: 'All',
    single: 'Single',
    team: 'Team',
    webSearch: 'Web Search',
    calculator: 'Calculator',
    codeInterpreter: 'Code Interpreter',
    gpt4: 'GPT-4',
    gpt35: 'GPT-3.5',
    claude: 'Claude',
    gemini: 'Gemini',
  },
  dialogs: {
    confirmDelete: 'Are you sure?',
    deleteTeam: 'Delete Team?',
    deleteTeamConfirm: 'Are you sure you want to delete this team?',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
  },
};
