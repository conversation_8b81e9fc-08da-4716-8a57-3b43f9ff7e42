import {
  <PERSON>,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Stack,
  Typography,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import LongMenu from 'src/components/long-menu';
import { StyledMarkdown } from 'src/components/mark-down/mark-down';
import { useTable } from 'src/components/table';
import { AppTablePropsType } from 'src/components/table/app-table/types/app-table';
import {
  ScheduledTaskItem,
  scheduleTaskEndpoints,
  useTasksSchedularApi,
} from 'src/services/api/use-task-schedular-api';
import { fDate } from 'src/utils/format-time';
import ScheduledTaskDialog from 'src/sections/scheduled-tasks/view/components/scheduled-task-dialog';

const useScheduledTasksTiew = () => {
  const { t } = useTranslation();
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openToggleDialog, setOpenToggleDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ScheduledTaskItem | null>(null);
  const [openResultDialog, setOpenResultDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);

  const queryClient = useQueryClient();
  const table = useTable();
  // API hooks
  const { useGetScheduledTasks, useDeleteShceduledTask, useToogleScheduledTask } =
    useTasksSchedularApi();
  const { data: tasksData, isLoading } = useGetScheduledTasks({
    take: table.rowsPerPage,
    skip: table.rowsPerPage * (table.page - 1),
  });
  const { mutate: deleteTask, isPending: isDeleting } = useDeleteShceduledTask();
  const { mutate: toggleTask, isPending: isToggling } = useToogleScheduledTask(
    selectedTask?.id?.toString() || ''
  );

  // Table configuration

  // Dialog handlers
  const handleOpenDeleteDialog = (task: ScheduledTaskItem) => {
    setSelectedTask(task);
    setOpenDeleteDialog(true);
  };

  const handleOpenToggleDialog = (task: ScheduledTaskItem) => {
    setSelectedTask(task);
    setOpenToggleDialog(true);
  };

  const handleOpenResultDialog = (task: ScheduledTaskItem) => {
    setSelectedTask(task);
    setOpenResultDialog(true);
  };

  const handleOpenEditDialog = (task: ScheduledTaskItem) => {
    setSelectedTask(task);
    setOpenEditDialog(true);
  };

  const handleCloseResultDialog = () => {
    setOpenResultDialog(false);
  };

  const handleConfirmDelete = () => {
    if (selectedTask?.id) {
      deleteTask(selectedTask.id, {
        onSuccess: () => {
          setOpenDeleteDialog(false);
          setSelectedTask(null);
        },
      });
    }
  };

  const handleConfirmToggle = () => {
    if (selectedTask?.id) {
      toggleTask(
        {},
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [scheduleTaskEndpoints.list + 'list'] });
            setOpenToggleDialog(false);
            setSelectedTask(null);
          },
        }
      );
    }
  };

  // Menu options for each row
  const getMenuOptions = (task: ScheduledTaskItem) => {
    const options: any[] = [
      {
        label: task.isActive ? t('status.paused') : t('status.active'),
        icon: task.isActive ? 'eva:pause-circle-fill' : 'eva:play-circle-fill',
        onClick: () => handleOpenToggleDialog(task),
        color: task.isActive ? 'warning.main' : 'success.main',
      },
      {
        label: t('buttons.edit'),
        icon: 'eva:edit-fill',
        onClick: () => handleOpenEditDialog(task),
        color: 'inherit',
      },
    ];

    const hasResults = !!task.task?.metadata?.results?.length;
    if (hasResults) {
      options.push({
        label: t('buttons.view'),
        icon: 'eva:eye-outline',
        onClick: () => handleOpenResultDialog(task),
        color: 'info.main',
      });
    }

    options.push({
      label: t('buttons.delete'),
      icon: 'eva:trash-2-outline',
      onClick: () => handleOpenDeleteDialog(task),
      color: 'error.main',
    });

    return options;
  };

  // ✅ Destructured arguments instead of data.prop
  const columns: AppTablePropsType<ScheduledTaskItem>['columns'] = [
    {
      name: 'task.content',
      PreviewComponent: ({ task }) => (
        <Box sx={{ maxWidth: 300 }}>
          <Typography variant="body2" noWrap>
            {task.content}
          </Typography>
        </Box>
      ),
    },
    {
      name: 'repeatType',
      PreviewComponent: ({ repeatType }) => (
        <Chip
          label={repeatType}
          size="small"
          sx={{
            bgcolor:
              repeatType === 'DAILY' ? '#E3F2FD' : repeatType === 'WEEKLY' ? '#F3E5F5' : '#E8F5E8',
            color:
              repeatType === 'DAILY' ? '#1976D2' : repeatType === 'WEEKLY' ? '#7B1FA2' : '#388E3C',
            fontWeight: 500,
            ':hover': {
              color: 'white',
            },
          }}
        />
      ),
    },
    {
      name: 'startDate',
      PreviewComponent: ({ startDate }) => (
        <Typography variant="body2">
          {' '}
          {fDate(startDate)}{' '}
          {new Date(startDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Typography>
      ),
    },
    {
      name: 'endDate',
      PreviewComponent: ({ endDate }) => (
        <Typography variant="body2">
          {fDate(endDate)}{' '}
          {new Date(endDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Typography>
      ),
    },
    {
      name: 'nextRun',
      PreviewComponent: ({ nextRun }) => (
        <Typography variant="body2">
          {' '}
          {nextRun ? fDate(nextRun) : ''}{' '}
          {nextRun
            ? new Date(nextRun).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            : 'N/A'}
        </Typography>
      ),
    },
    {
      name: 'lastRun',
      PreviewComponent: ({ lastRun }) => (
        <Typography variant="body2">
          {lastRun ? fDate(lastRun) : ''}{' '}
          {lastRun
            ? new Date(lastRun).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            : 'N/A'}
        </Typography>
      ),
    },
    {
      name: 'interval',
      PreviewComponent: ({ interval }) => <Typography variant="body2">{interval}</Typography>,
    },
    {
      name: 'isActive',
      PreviewComponent: ({ isActive }) => (
        <Chip
          label={isActive ? t('status.active') : t('status.paused')}
          size="small"
          sx={{
            bgcolor: isActive ? '#E8F5E8' : '#FFEBEE',
            color: isActive ? '#2E7D32' : '#D32F2F',
            fontWeight: 500,
            ':hover': {
              color: 'white',
            },
          }}
        />
      ),
    },
    {
      name: 'task.status',
      PreviewComponent: ({ task }) => {
        const status = task?.status || 'UNKNOWN';
        const styles =
          status === 'COMPLETED' || status === 'SUCCESS'
            ? { bgcolor: '#E8F5E8', color: '#2E7D32' }
            : status === 'FAILED' || status === 'ERROR'
              ? { bgcolor: '#FFEBEE', color: '#D32F2F' }
              : status === 'RUNNING' || status === 'IN_PROGRESS'
                ? { bgcolor: '#FFF8E1', color: '#F57C00' }
                : { bgcolor: '#E3F2FD', color: '#1976D2' };
        return (
          <Chip
            label={status}
            size="small"
            sx={{
              ...styles,
              fontWeight: 500,
              textTransform: 'capitalize',
              ':hover': {
                color: 'white',
              },
            }}
          />
        );
      },
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (task) => {
        const isDialogOpen = openResultDialog && selectedTask?.id === task?.id;
        const isEditDialogOpen = openEditDialog && selectedTask?.id === task?.id;
        const lastResult = selectedTask?.task?.metadata?.results?.length
          ? selectedTask.task.metadata.results[selectedTask.task.metadata.results.length - 1]
          : null;
        const success = lastResult?.success;
        return (
          <>
            <LongMenu options={getMenuOptions(task)} />
            <ScheduledTaskDialog
              open={!!isEditDialogOpen}
              onClose={() => setOpenEditDialog(false)}
              mode="edit"
              scheduledTask={selectedTask}
            />
            <Dialog fullWidth maxWidth="md" open={!!isDialogOpen} onClose={handleCloseResultDialog}>
              <DialogTitle>{t('messages.taskResult')}</DialogTitle>
              <DialogContent dividers>
                {lastResult ? (
                  <Stack spacing={2}>
                    <Stack direction="row" spacing={1} alignItems="center" flexWrap="wrap">
                      <Chip
                        label={success ? t('status.success') : t('status.failed')}
                        size="small"
                        sx={{
                          bgcolor: success ? '#E8F5E8' : '#FFEBEE',
                          color: success ? '#2E7D32' : '#D32F2F',
                          fontWeight: 500,
                          ':hover': {
                            color: 'white',
                          },
                        }}
                      />
                      {lastResult.executedAt && (
                        <Chip
                          label={`Executed: ${fDate(lastResult.executedAt)}`}
                          size="small"
                          sx={{
                            bgcolor: '#E3F2FD',
                            color: '#1976D2',
                            fontWeight: 500,
                            ':hover': {
                              color: 'white',
                            },
                          }}
                        />
                      )}
                      <Chip
                        label={`Retries: ${lastResult.retryCount ?? 0}`}
                        size="small"
                        sx={{
                          bgcolor: '#F3E5F5',
                          color: '#7B1FA2',
                          fontWeight: 500,
                          ':hover': {
                            color: 'white',
                          },
                        }}
                      />
                      <Chip
                        label={`Executions: ${lastResult.executionCount ?? 0}`}
                        size="small"
                        sx={{
                          bgcolor: '#E8F5E8',
                          color: '#388E3C',
                          fontWeight: 500,
                          ':hover': {
                            color: 'white',
                          },
                        }}
                      />
                    </Stack>
                    <Box
                      sx={{
                        p: 2,
                        bgcolor: '#FAFAFA',
                        borderRadius: 1,
                        border: '1px solid #EEE',
                        maxHeight: 400,
                        overflow: 'auto',
                        fontFamily: 'monospace',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                      }}
                    >
                      <StyledMarkdown content={lastResult.result} />
                    </Box>
                  </Stack>
                ) : (
                  <Typography variant="body2">{t('messages.anErrorOccurred')}</Typography>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseResultDialog} variant="contained">
                  {t('buttons.cancel')}
                </Button>
              </DialogActions>
            </Dialog>
          </>
        );
      },
    },
  ];

  const headLabels = [
    { id: 'task.content', label: t('table.taskContent') },
    { id: 'repeatType', label: t('table.repeatType') },
    { id: 'startDate', label: t('table.startDate') },
    { id: 'endDate', label: t('table.endDate') },
    { id: 'nextRun', label: t('table.nextRun') },
    { id: 'lastRun', label: t('table.lastRun') },
    { id: 'interval', label: t('table.interval') },
    { id: 'isActive', label: t('table.status') },
    { id: 'task.status', label: t('table.taskStatus') },
    { id: 'actions', label: t('table.actions') },
  ];

  return {
    headLabels,
    tasksData,
    isLoading,
    columns,
    table,
    openDeleteDialog,
    setOpenDeleteDialog,
    handleConfirmDelete,
    isDeleting,
    openToggleDialog,
    setOpenToggleDialog,
    selectedTask,
    handleConfirmToggle,
    isToggling,
    openResultDialog,
    setOpenResultDialog,
  };
};

export default useScheduledTasksTiew;
