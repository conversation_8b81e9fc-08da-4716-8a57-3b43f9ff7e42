import { z as zod } from 'zod';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import { useTheme } from '@mui/material/styles';

import { AppButton } from 'src/components/common';
import { useBoolean } from 'src/hooks/use-boolean';
import { Iconify } from 'src/components/iconify';
import { RouterLink } from 'src/routes/components';
import { paths } from 'src/routes/paths';
import { resetPassword } from 'src/auth/context/jwt'; // Actual API for requesting code

import { Form, Field } from 'src/components/hook-form';
import { CONFIG } from 'src/config-global';
import { Card } from '@mui/material';
import { useRouter } from 'src/hooks/use-router';
import { useSnackbar } from 'src/components/snackbar';
import { enqueueSnackbar } from 'notistack';

// Mock API function for setting new password (replace with actual API call)

// Schema for requesting a reset code (email only)
export const RequestCodeSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
});
export type RequestCodeSchemaType = zod.infer<typeof RequestCodeSchema>;

// Schema for setting a new password (code, new password, confirm password)

// ----------------------------------------------------------------------

export function JwtResetPasswordView() {
  const { t } = useTranslation();
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState<'enterEmail' | 'success'>('enterEmail');
  const currentLng = localStorage.getItem('i18nextLng') || 'en';

  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const requestCodeMethods = useForm<RequestCodeSchemaType>({
    resolver: zodResolver(RequestCodeSchema),
    defaultValues: { email: '' },
  });
  const handleResetPasswordSuccessMessage = () => {
    enqueueSnackbar('Reset link sent to your email.', {
      variant: 'default',

      anchorOrigin: {
        vertical: 'bottom',
        horizontal: currentLng === 'en' ? 'right' : 'left',
      },
    });
  };

  const onRequestCodeSubmit = requestCodeMethods.handleSubmit(async (data) => {
    try {
      setErrorMsg('');
      setSuccessMsg('');
      await resetPassword({ email: data.email }); // Use actual API call
      // setCurrentStep('success');
      handleResetPasswordSuccessMessage?.();
      // router.push(paths.auth.jwt.newPassword);
    } catch (error) {
      console.error(error);
      setErrorMsg(typeof error === 'string' ? error : (error as Error).message);
    }
  });

  const renderBackButton = () => (
    <Link
      component={RouterLink}
      href={paths.auth.jwt.signIn}
      variant="body2"
      sx={{
        display: 'flex',
        alignItems: 'center',
        textDecoration: 'none',
        my: 3,
        '&:hover': {
          textDecoration: 'underline',
        },
      }}
    >
      <Iconify
        icon="eva:arrow-back-fill"
        width={20}
        sx={{ mr: 0.5, color: theme.palette.grey[600] }}
      />
      <Typography color="rgba(15, 14, 17, 1)">{t('auth.resetPasswordView.backToLogin')}</Typography>
    </Link>
  );

  const renderHead = () => (
    <Box justifyContent="center" sx={{ mb: 1 }} display="flex">
      <Box
        component="img"
        src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
        sx={{ width: 'auto', height: 44 }}
      />
      <Typography fontWeight="bold" variant="h3" p="6px" lineHeight="32px">
        {t('auth.resetPasswordView.workforces')}
      </Typography>
    </Box>
  );
  const renderEnterEmailForm = () => (
    <Form methods={requestCodeMethods} onSubmit={onRequestCodeSubmit}>
      <Stack spacing={2.5}>
        <Typography variant="h5" sx={{ fontWeight: 700 }}>
          {t('auth.resetPasswordView.title')}
        </Typography>
        <Typography variant="body1" sx={{ color: 'rgba(15, 14, 17, 1)', mb: 2 }}>
          Enter your email, and we&apos;ll send you a verification code.
        </Typography>

        {!!errorMsg && (
          <Alert severity="error" sx={{ mb: 1 }}>
            {errorMsg}
          </Alert>
        )}

        <Card
          sx={{
            border: '1px solid rgba(224, 223, 226, 1)',
            borderRadius: '12px',
            p: 2,
            backgroundColor: 'white',
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            width: '100%',
          }}
        >
          {/* Email Field */}
          <Controller
            name="email"
            defaultValue=""
            render={({ field }) => (
              <Field.Text
                {...field}
                label="Email Address"
                placeholder="Enter valid email address"
                fullWidth
                variant="standard"
                InputProps={{
                  disableUnderline: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify
                        icon="mdi:mail-outline"
                        width={30}
                        sx={{
                          mr: 2,
                          transform: 'translateY(-14px)',
                          color: 'rgba(144, 108, 229, 1)',
                        }}
                      />
                    </InputAdornment>
                  ),
                  sx: {
                    px: 1,

                    height: 35,
                    borderRadius: 2,
                  },
                }}
                InputLabelProps={{
                  style: {
                    fontSize: '1.1rem',
                    color: 'rgba(70, 70, 70, 1)',
                  },
                  sx: {
                    color: 'red',
                    px: 10,
                  },
                  shrink: true,
                }}
              />
            )}
          />
        </Card>

        <AppButton
          fullWidth
          size="large"
          type="submit"
          variant="contained"
          label="Reset Password"
          isLoading={requestCodeMethods.formState.isSubmitting}
          sx={{
            backgroundColor: 'rgba( 163 ,99 ,233)', // Purple color from image
            color: theme.palette.common.white,
            borderRadius: '8px',
            fontWeight: 300,
            fontSize: '1rem',
            height: '48px',
            textTransform: 'none',
            '&:hover': {
              backgroundColor: '#6D28D9', // Darker purple for hover
            },
          }}
        />
      </Stack>
    </Form>
  );

  // const renderEnterCodeAndPasswordForm = () => (
  //   <Form methods={setNewPasswordMethods} onSubmit={onSetNewPasswordSubmit}>
  //     <Stack spacing={0.5}>
  //       <Typography component="span" sx={{ fontWeight: 'bold', color: 'black' }}>
  //         Reset your password
  //       </Typography>
  //       <Typography variant="body2" sx={{ color: 'rgba(15, 14, 17, 1)', mb: 2 }}>
  //         We&apos;ve sent a 4-digit code to{' '}
  //         <Typography
  //           component="span"
  //           sx={{ display: 'block', fontWeight: 'bold', color: 'black' }}
  //         >
  //           {emailForReset || 'your email'}
  //         </Typography>
  //         <Typography
  //           variant="body1"
  //           component="label"
  //           htmlFor="code"
  //           sx={{ mt: 3, color: 'rgba(15, 14, 17, 1)', display: 'block' }}
  //         >
  //           Please enter it below.
  //         </Typography>
  //       </Typography>

  //       {!!errorMsg && (
  //         <Alert severity="error" sx={{ mb: 1 }}>
  //           {errorMsg}
  //         </Alert>
  //       )}

  //       <Field.Code
  //         name="code"
  //         length={4}
  //         sx={{
  //           display: 'flex',
  //           gap: 1.5, // Spacing between individual input boxes
  //           justifyContent: 'center', // Center the boxes
  //           mb: 1, // Add some margin if needed
  //           '& .MuiTextField-root': {
  //             // Target each individual input box
  //             width: { sm: '100%' }, // Responsive width for smaller screens
  //             '& .MuiInputBase-root': {
  //               height: { xs: 40, sm: 60 },
  //               borderRadius: '8px', // Match other inputs
  //               backgroundColor: theme.palette.common.white,
  //               boxShadow: `0 0 0 1px ${theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300]} inset`,
  //               '& input': {
  //                 textAlign: 'center',
  //                 padding: 0,
  //                 fontSize: { xs: '1rem', sm: '1.25rem' },
  //                 fontWeight: 300,
  //                 color: theme.palette.text.primary,
  //               },
  //             },
  //             '& .MuiOutlinedInput-notchedOutline': {
  //               // Hide default borders if custom shadow is used
  //               border: 'none',
  //             },
  //             '&.Mui-focused .MuiInputBase-root': {
  //               // Style for focused state
  //               boxShadow: `0 0 0 2px ${theme.palette.primary.main} inset`,
  //             },
  //             '&.Mui-error .MuiInputBase-root': {
  //               // Style for error state
  //               boxShadow: `0 0 0 2px ${theme.palette.error.main} inset`,
  //             },
  //           },
  //         }}
  //       />

  //       <AppButton
  //         fullWidth
  //         size="large"
  //         type="submit"
  //         variant="contained"
  //         label="Set New Password"
  //         isLoading={setNewPasswordMethods.formState.isSubmitting}
  //         sx={{
  //           backgroundColor: 'rgba( 163 ,99 ,233)',
  //           color: theme.palette.common.white,
  //           borderRadius: '8px',
  //           fontWeight: 300,
  //           fontSize: '1rem',
  //           height: '48px',
  //           textTransform: 'none',
  //           '&:hover': {
  //             backgroundColor: '#6D28D9',
  //           },
  //         }}
  //       />
  //     </Stack>
  //   </Form>
  // );

  const renderSuccessDisplay = () => (
    <Stack spacing={2} alignItems="center" sx={{ textAlign: 'center' }}>
      <Iconify icon="solar:check-circle-bold" width={128} sx={{ color: 'success.main', mb: 2 }} />
      <Typography variant="h5" sx={{ fontWeight: 300, color: theme.palette.grey[800] }}>
        Done
      </Typography>
      <Typography variant="body2" sx={{ color: 'black', fontWeight: '700', mb: 2 }}>
        {successMsg || 'Your password has been successfully changed.'}
      </Typography>
      <AppButton
        component={RouterLink}
        href={paths.auth.jwt.signIn}
        label="Back to login"
        variant="contained"
        sx={{
          backgroundColor: 'rgba( 163 ,99 ,233)',
          color: theme.palette.common.white,
          borderRadius: '8px',
          fontWeight: 300,
          fontSize: '1rem',
          height: '40px',
          textTransform: 'none',
          px: 5,
          '&:hover': {
            backgroundColor: '#6D28D9',
          },
        }}
      />
    </Stack>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: { xs: 5, md: 0 },
      }}
    >
      <Box
        sx={{
          width: '100%',
          maxWidth: 456, // As per image proportions
          p: { xs: '30px', md: '40px' }, // Padding from image
          borderRadius: '12px', // Rounded corners from image
          bgcolor: theme.palette.common.white,
          boxShadow: '0px 20px 40px rgba(0, 0, 0, 0.2)', // Softer shadow
        }}
      >
        {currentStep === 'enterEmail' && renderHead()}
        {currentStep !== 'success' && renderBackButton()}

        {currentStep === 'enterEmail' && renderEnterEmailForm()}
        {/* {/* {currentStep === 'enterCodeAndPassword' && renderEnterCodeAndPasswordForm()} */}
        {currentStep === 'success' && renderSuccessDisplay()}
      </Box>
    </Box>
  );
}
