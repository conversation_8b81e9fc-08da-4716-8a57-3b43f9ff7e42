import {
  Card,
  CardContent,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from '@mui/material';
import { useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Iconify } from 'src/components/iconify';
// import { TEAM_MODEL_OPTIONS } from 'src/services/api/use-templates-teams-api';
// import { TeamFormValues } from '../../view/use-teams-view';
import { LLM_MODEL_OPTIONS } from 'src/constants/constants';
import { TeamFormValues } from 'src/services/api/use-teams-api';
import ServiceSearchBar from '../../../agents/form/components/service-search-bar';

// ----------------------------------------------------------------------

interface ModelStepProps {
  // Add any specific props if needed
}

export function ModelStep(_props: ModelStepProps) {
  const { setValue, watch } = useFormContext<TeamFormValues>();
  const [searchQuery, setSearchQuery] = useState('');

  // Watch current selection
  const selectedModel = watch('model');

  // Filter models based on search
  const filteredModels = useMemo(() => {
    if (!searchQuery) return LLM_MODEL_OPTIONS;

    return LLM_MODEL_OPTIONS.filter(
      (model) =>
        model.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.value.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  // Handle model selection
  const handleModelSelect = (modelValue: string) => {
    setValue('model', modelValue as any, { shouldValidate: true });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Get icon for model
  const getModelIcon = (modelValue: string) => {
    if (modelValue.includes('GPT')) return 'simple-icons:openai';
    if (modelValue.includes('CLAUDE')) return 'simple-icons:anthropic';
    if (modelValue.includes('GEMINI')) return 'hugeicons:google-gemini';
    return 'mdi:robot';
  };

  return (
    <>
      <Typography color="rgba(15, 14, 17, 0.65)" variant="h5" sx={{ mb: '20px' }}>
        Choose AI model for your team
      </Typography>
      <Stack spacing={2} bgcolor="white" p="20px" borderRadius="10px">
        <ServiceSearchBar
          query={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search models..."
        />
        <RadioGroup
          value={selectedModel || ''}
          onChange={(event) => handleModelSelect(event.target.value)}
        >
          <Grid container spacing={2}>
            {filteredModels.map((model) => {
              const isSelected = selectedModel === model.value;
              return (
                <Grid item xs={12} sm={6} md={12} key={model.value}>
                  <Card
                    variant="outlined"
                    sx={{
                      cursor: 'pointer',
                      bgcolor: 'divider',
                      border: isSelected ? '2px solid' : '1px solid',
                      borderColor: isSelected ? 'primary.main' : 'divider',
                    }}
                    onClick={() => handleModelSelect(model.value)}
                  >
                    <CardContent>
                      <FormControlLabel
                        control={<Radio checked={isSelected} />}
                        label={
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify icon={getModelIcon(model.value)} sx={{ color: 'inherit' }} />
                            <Stack>
                              <Typography variant="subtitle2">{model.label}</Typography>
                            </Stack>
                          </Stack>
                        }
                        sx={{ width: '100%', margin: 0 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </RadioGroup>
      </Stack>
    </>
  );
}

export default ModelStep;
