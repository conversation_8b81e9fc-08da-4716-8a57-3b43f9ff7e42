import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const scheduleTaskEndpoints = {
  list: `/tasks/schedule`,
  details: `/tasks/schedule`,
  toggle: (sid: number | string) => `/tasks/schedule/${sid}/toggle`,
  delete: (sid: number | string) => `/tasks/schedule/${sid}`,
};

export type ScheduledTaskItem = {
  id: number;
  taskId: number;
  repeatType: 'MONTHLY' | 'WEEKLY' | 'DAILY'; // enum from your schema
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  lastRun: string | null;
  nextRun: string | null;
  interval: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  task: {
    id: number;
    chatId: number;
    content: string;
    senderId: number;
    status: string;
    agentId: number;
    userTeamId: number | null;
    createdAt: string;
    updatedAt: string;
    metadata: {
      results: ResUltMetaData[];
    };
  };
};
export type ResUltMetaData = {
  result: string; // "Here is a simple C++ program that prints \"Hello\":\n\n```cpp\n#include <iostream>\n\nint main() {\n    std::cout << \"Hello\" << std::endl;\n    return 0;\n}\n```\n\nYou can compile and run this code using a C++ compiler. If you need further assistance, feel free to ask! \n\nTERMINATE",
  success: boolean;
  executedAt: string;
  retryCount: number;
  executionCount: number;
};
// Define the Category interface
export type ScheduledTaskResponse = {
  tasksScheduled: ScheduledTaskItem[];
  total: number;
};

export type ScheduledTask = {
  task: string;
  repeatType: string;
  startDate: string;
  endDate: string;
  interval: number;
};

// Define the API response structure
export interface TaskScheduledFilters {
  take?: number;
  skip?: number;
}
// Create a hook to use the Agentss API
export const useTasksSchedularApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss
  const useGetScheduledTasks = (apiFilters?: TaskScheduledFilters) => {
    return apiServices.useGetListService<ScheduledTaskResponse, TaskScheduledFilters>({
      url: scheduleTaskEndpoints.list,
      params: apiFilters,
    });
  };

  const useShceduledTask = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<ScheduledTask, any>({
      url: scheduleTaskEndpoints.list,
      onSuccess,
    });
  };

  // Update a MyTeamTemplatess
  const useUpdateScheduledTask = (sid: string | number, onSuccess?: () => void) => {
    return apiServices.usePatchService<ScheduledTask>({
      url: scheduleTaskEndpoints.list,
      id: sid?.toString(),
      onSuccess,
    });
  };
  // Update a MyTeamTemplatess
  const useToogleScheduledTask = (sid: string, onSuccess?: () => void) => {
    return apiServices.usePatchService<any>({
      url: scheduleTaskEndpoints.toggle(sid),
      onSuccess,
    });
  };

  // Delete a ShceduledTask
  const useDeleteShceduledTask = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number | string>({
      url: scheduleTaskEndpoints.details,
      onSuccess,
    });
  };
  return {
    useGetScheduledTasks,
    useShceduledTask,
    useDeleteShceduledTask,
    useToogleScheduledTask,
    useUpdateScheduledTask,
  };
};
