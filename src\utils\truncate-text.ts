export function truncateText(text: string, maxWords: number = 50): string {
  const words = text.split(' ');

  if (words.length === 1 && text.length > 26) {
    return text.slice(0, 26) + '.....';
  }

  if (words.length > maxWords) {
    return words.slice(0, maxWords).join(' ') + '.....';
  }

  return text;
}

export function getFirstWords(text: string, count: number): string {
  const words = text.trim().split(/\s+/);
  if (words.length <= count) {
    return words.join(' ');
  }
  return words.slice(0, count).join(' ') + ' .....';
}
