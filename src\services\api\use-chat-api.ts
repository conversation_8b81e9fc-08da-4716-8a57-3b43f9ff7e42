import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Chats
export const ChatEndpoints = {
  list: '/chats',
  details: '/chats',
};

export interface ChatBody {
  title: string;
  userTeamId?: number | string;
  agentId?: number | string;
}
// Define the Category interface
export type ChatRes = {
  id: number;
  userId: number;
  title: string;
  agentId: number;
  createdAt: string;
  status?: string;
  taskCount?: number;
};
export interface Chat {
  chats: ChatRes[];
  total: number;
}
export interface ChatResponse {
  chat: any;
}

// Define the API response structure

// Create a hook to use the Chats API
export const useChatApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Chats
  const useGetChats = (id: string | number, name: 'agent' | 'team' = 'agent') => {
    return apiServices.useGetListService<
      Chat,
      {
        agentId: string | number | undefined;
        userTeamId: string | number | undefined;
        take: number;
      }
    >({
      url: ChatEndpoints.list,
      params: {
        userTeamId: name !== 'agent' ? id : undefined,
        agentId: name === 'agent' ? id : undefined,
        take: 50,
      },
    });
  };

  // Get a single Chat by ID
  const useGetChat = (id: string) => {
    return apiServices.useGetItemService<Chat>({
      url: ChatEndpoints.details,
      id,
    });
  };

  // Create a new Chat
  const useCreateChat = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<ChatBody, any>({
      url: ChatEndpoints.list,
      onSuccess,
    });
  };

  // Update a Chat
  const useUpdateChat = (id: string | number, onSuccess?: () => void) => {
    return apiServices.usePatchService<{ title: string }>({
      url: ChatEndpoints.details,
      id: id?.toString(),
      onSuccess,
    });
  };

  // Delete a Chat
  const useDeleteChat = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<any, number>({
      url: ChatEndpoints.details,
      urlAfterSuccess: ChatEndpoints.list + 'list',
      onSuccess,
    });
  };

  return {
    useGetChats,
    useGetChat,
    useCreateChat,
    useUpdateChat,
    useDeleteChat,
  };
};
