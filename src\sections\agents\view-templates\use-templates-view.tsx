import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { usePermissions } from 'src/auth/hooks';
import { useTable } from 'src/components/table';
import { useDebounce } from 'src/hooks/use-debounce';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { Template, TemplateFilters, useTemplatesApi } from 'src/services/api/use-templates-api';

// Filter options for table view
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const TOOLS_FILTERS = ['All', 'Web Search', 'Calculator', 'Code Interpreter'];
const MODEL_FILTERS = ['All', 'GPT-4', 'GPT-3.5', 'Claude', 'Gemini'];
const STATUS_FILTERS = ['All', 'ACTIVE', 'INACTIVE'];

export const useAgentView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Template[]>([]);
  const [selectedTab, setSelectedTab] = useState(0); // 0 = Cards (PUBLIC), 1 = Table (PRIVATE)

  // Infinite scroll state for cards view (tab 0)
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);

  const table = useTable();

  // Debounce search query for API calls (400ms delay)
  const debouncedSearchQuery = useDebounce(searchQuery, 2000);

  // Cards view filters for public templates
  const [selectedCardsTypeFilter, setSelectedCardsTypeFilter] = useState(0);
  const [selectedCardsCategoryFilter, setSelectedCardsCategoryFilter] = useState(0);

  // Table filters for private templates
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);
  const [selectedToolsFilter, setSelectedToolsFilter] = useState(0);
  const [selectedModelFilter, setSelectedModelFilter] = useState(0);
  const [selectedStatusFilter, setSelectedStatusFilter] = useState(0);

  const { t } = useTranslation();

  const { canCreate } = usePermissions();
  // Use the templates API hook to fetch data with visibility filter
  const { useGetTemplates } = useTemplatesApi();

  // Use the categories API hook to fetch categories
  const { useGetCategories } = useCategoriesApi();
  const { data: categoriesData, isLoading: categoriesLoading } = useGetCategories();

  // Create dynamic category filters with useMemo to prevent infinite re-renders
  const CATEGORY_FILTERS = useMemo(() => {
    if (
      !categoriesData ||
      !categoriesData.categories ||
      !Array.isArray(categoriesData.categories)
    ) {
      return ['All'];
    }
    return ['All', ...categoriesData.categories.map((category) => category.name)];
  }, [categoriesData]);

  // Determine current visibility based on selected tab
  const currentVisibility = selectedTab === 0 ? 'PUBLIC' : 'PRIVATE';

  // Build filters for API call
  const apiFilters: TemplateFilters = {
    visibility: currentVisibility,
    type: 'SINGLE',
    // For cards view (tab 0), use infinite scroll pagination
    // For table view (tab 1), use regular table pagination
    take: selectedTab === 0 ? 10 : table.rowsPerPage,
    skip: selectedTab === 0 ? currentPage * 10 : (table.page - 1) * table.rowsPerPage,
    // Add search query if present (backend search)
    ...(debouncedSearchQuery && { name: debouncedSearchQuery }),
    ...(selectedTab === 0 && {
      // Cards view filters (public templates)
      ...(selectedCardsTypeFilter !== 0 && { type: TYPE_FILTERS[selectedCardsTypeFilter] }),
      ...(selectedCardsCategoryFilter !== 0 && {
        categoryId: categoriesData?.categories?.[selectedCardsCategoryFilter - 1]?.id
          ? categoriesData.categories[selectedCardsCategoryFilter - 1].id
          : undefined,
      }),
    }),
    ...(selectedTab === 1 && {
      // Table view filters (private templates)
      ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
      ...(selectedCategoryFilter !== 0 && {
        categoryId: categoriesData?.categories?.[selectedCategoryFilter - 1]?.id
          ? categoriesData.categories[selectedCategoryFilter - 1].id
          : undefined,
      }),
      ...(selectedToolsFilter !== 0 && { tools: TOOLS_FILTERS[selectedToolsFilter] }),
      ...(selectedModelFilter !== 0 && { model: MODEL_FILTERS[selectedModelFilter] }),
      ...(selectedStatusFilter !== 0 && { status: STATUS_FILTERS[selectedStatusFilter] }),
    }),
  };

  const { data: templatesResponse, isLoading, error, refetch } = useGetTemplates(apiFilters);

  // Extract templates from the response
  const templates = templatesResponse?.templates || [];
  const countsOfTheTemplates = templatesResponse?.total || 0;

  // Update filtered templates when templates data changes
  useEffect(() => {
    if (selectedTab === 0) {
      // Cards view: Handle infinite scroll
      if (currentPage === 0) {
        // First page or reset - replace all templates
        setFilteredAgents(templates);
      } else {
        // Subsequent pages - append new templates
        setFilteredAgents((prev) => [...prev, ...templates]);
      }

      // Update pagination state based on total count and current loaded count
      setFilteredAgents((current) => {
        const currentLoadedCount = current.length;
        setHasNextPage(currentLoadedCount < countsOfTheTemplates && templates.length === 10);
        return current;
      });
      setIsLoadingMore(false);
    } else {
      // Table view: Replace all templates (regular pagination)
      setFilteredAgents(templates);
    }
  }, [templates, selectedTab, currentPage, countsOfTheTemplates]);

  // Load more templates for infinite scroll
  const loadMoreTemplates = useCallback(() => {
    if (
      selectedTab === 0 &&
      hasNextPage &&
      !isLoadingMore &&
      !isLoading &&
      filteredAgents.length < countsOfTheTemplates
    ) {
      console.log('Loading more templates, current page:', currentPage); // Debug log
      setIsLoadingMore(true);
      if (!isLoading) setCurrentPage((prev) => prev + 1);
    }
  }, [
    selectedTab,
    hasNextPage,
    isLoadingMore,
    isLoading,
    filteredAgents.length,
    countsOfTheTemplates,
    currentPage, // Add currentPage to dependencies to prevent stale closure
  ]);

  // Throttle flag to prevent rapid scroll events
  const [isScrollThrottled, setIsScrollThrottled] = useState(false);

  // Scroll detection for infinite scroll
  const handleScroll = useCallback(() => {
    if (selectedTab !== 0 || isScrollThrottled) return; // Only for cards view and not throttled

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = window.innerHeight;

    // Load more when user is 200px from bottom
    if (scrollTop + clientHeight >= scrollHeight - 200) {
      setIsScrollThrottled(true);
      loadMoreTemplates();
      // Reset throttle after 100ms
      setTimeout(() => setIsScrollThrottled(false), 100);
    }
  }, [selectedTab, loadMoreTemplates, isScrollThrottled]);

  // Add scroll event listener
  useEffect(() => {
    if (selectedTab !== 0) return () => {}; // Return empty cleanup for non-cards view

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [selectedTab, handleScroll]);

  // Reset infinite scroll state when filters change
  useEffect(() => {
    if (selectedTab === 0) {
      setCurrentPage(0);
      setHasNextPage(true);
      setIsLoadingMore(false);
      setIsScrollThrottled(false); // Reset scroll throttle
    }
  }, [selectedTab, debouncedSearchQuery, selectedCardsTypeFilter, selectedCardsCategoryFilter]);

  // Handle search for both views
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Handle main tab change (Cards vs Table)
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setSearchQuery(''); // Reset search when switching tabs
    // Reset filters when switching tabs
    if (newValue === 0) {
      // Reset table filters
      setSelectedTypeFilter(0);
      setSelectedCategoryFilter(0);
      setSelectedToolsFilter(0);
      setSelectedModelFilter(0);
      setSelectedStatusFilter(0);
    } else {
      // Reset cards filters
      setSelectedCardsTypeFilter(0);
      setSelectedCardsCategoryFilter(0);
    }
  };

  // Handle cards view filter changes
  const handleCardsTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsTypeFilter(newValue);
  };

  const handleCardsCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsCategoryFilter(newValue);
  };

  // Handle table filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  const handleToolsFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedToolsFilter(newValue);
  };

  const handleModelFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedModelFilter(newValue);
  };

  const handleStatusFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedStatusFilter(newValue);
  };

  return {
    // Data
    templates,
    filteredAgents,

    // Loading states
    isLoading,
    isInitialLoading: isLoading && currentPage === 0,
    error,
    refetch,
    categoriesLoading,

    // Infinite scroll states
    isLoadingMore,
    hasNextPage,
    loadMoreTemplates,

    // Tab and filter state
    selectedTab,
    searchQuery,

    // Cards view filters
    selectedCardsTypeFilter,
    selectedCardsCategoryFilter,

    // Table view filters
    selectedTypeFilter,
    selectedCategoryFilter,
    selectedToolsFilter,
    selectedModelFilter,
    selectedStatusFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    TOOLS_FILTERS,
    MODEL_FILTERS,
    STATUS_FILTERS,

    // Event handlers
    handleSearch,
    handleTabChange,

    // Cards view filter handlers
    handleCardsTypeFilterChange,
    handleCardsCategoryFilterChange,

    // Table view filter handlers
    handleTypeFilterChange,
    handleCategoryFilterChange,
    handleToolsFilterChange,
    handleModelFilterChange,
    handleStatusFilterChange,
    table,
    countsOfTheTemplates,
    currentPage,
    canCreate,
    t,
  };
};

export type { Template };
