import axios, { endpoints } from 'src/utils/axios';

import { setSession, clearAuthData } from './utils';

// ----------------------------------------------------------------------

export type SignInParams = {
  email: string;
  password: string;
};

export type SignUpParams = {
  email: string;
  password: string;
  username: string;
  name: string;
};

/** **************************************
 * Sign in
 *************************************** */
export const signInWithPassword = async ({ email, password }: SignInParams): Promise<void> => {
  try {
    const params = { email, password };

    const res = await axios.post(endpoints.auth.signIn, params);

    const { accessToken } = res.data;

    if (!accessToken) {
      throw new Error('Access token not found in response');
    }

    await setSession(accessToken);
  } catch (error) {
    console.error('Error during sign in:', error);
    throw error;
  }
};

/** **************************************
 * Sign up
 *************************************** */
export const signUp = async ({ email, password, name, username }: SignUpParams): Promise<void> => {
  const params = {
    email,
    password,
    name,
    username,
  };

  try {
    await axios.post(endpoints.auth.signUp, params);
  } catch (error) {
    console.error('Error during sign up:', error);
    throw error;
  }
};

/** **************************************
 * Sign out
 *************************************** */
export const signOut = async (): Promise<void> => {
  try {
    // Call backend logout endpoint
    await axios.post(endpoints.auth.signOut);
  } catch (error) {
    // Even if backend call fails, we should clear local auth data
    console.error('Error during backend sign out:', error);
  } finally {
    // Always clear local authentication data
    clearAuthData();
  }
};
export const resetPassword = async ({ email }: { email: string }): Promise<void> => {
  try {
    const params = { email };
    await axios.post(endpoints.auth.forgetPassword, params);
  } catch (error) {
    console.error('Error during password reset:', error);
    throw error;
  }
};

export const refreshAccessToken = async (): Promise<string | null> => {
  try {
    const res = await axios.post(endpoints.auth.refreshToken);
    const { accessToken } = res.data;

    if (accessToken) {
      await setSession(accessToken);
      return accessToken;
    }

    return null;
  } catch (error: any) {
    console.error('Refresh token failed:', error);

    // If refresh token returns 401, it means refresh token is expired
    // Navigate to sign in immediately
    if (error.response?.status === 401) {
      clearAuthData();
      // Use setTimeout to avoid navigation during axios interceptor
      setTimeout(() => {
        window.location.href = '/auth/jwt/sign-in';
      }, 100);
    }

    return null;
  }
};

export const verifyEmail = async (email: string, token: string): Promise<void | null> => {
  const { data } = await axios.post(endpoints.auth.verifyEmail, {
    token,
    email,
  });

  return data;
};

export async function exchangeOAuthCode(code: string, state: string) {
  const { data } = await axios.post(endpoints.auth.exchangeOAuthCode, {
    code,
    state,
  });

  return data;
}

export async function changePassword(email: string, token: string, password: string) {
  const { data } = await axios.post(endpoints.auth.resetPassword, {
    email,
    token,
    password,
  });

  return data;
}
