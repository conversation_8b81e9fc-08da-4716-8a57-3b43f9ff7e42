import { useMemo, useEffect, useCallback } from 'react';

import { useSetState } from 'src/hooks/use-set-state';

import axios, { endpoints, setUnauthorizedCallback } from 'src/utils/axios';
import { useDeveloperMode } from 'src/contexts/developer-mode-context';

import { AuthContext } from '../auth-context';
import { setSession, isValidToken, getStoredToken, clearAuthData } from './utils';
import { refreshAccessToken } from './action';

import type { AuthState } from '../../types';

// ----------------------------------------------------------------------

/**
 * NOTE:
 * We only build demo at basic level.
 * Customer will need to do some extra handling yourself if you want to extend the logic and other features...
 */

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const { state, setState } = useSetState<AuthState>({
    user: null,
    loading: true,
  });

  const checkUserSession = useCallback(async () => {
    try {
      const accessToken = getStoredToken();

      if (accessToken && isValidToken(accessToken)) {
        // Set session with the found token
        await setSession(accessToken, true); // Enable persistent login

        try {
          const res = await axios.get(endpoints.user.me);
          const user = res.data;

          setState({ user: { ...user, accessToken }, loading: false });
        } catch (apiError) {
          // If user.me fails, token might be invalid
          console.error('Failed to fetch user data:', apiError);
          clearAuthData(); // Clear invalid session
          setState({ user: null, loading: false });
        }
      } else {
        // Clear any invalid tokens
        clearAuthData();
        setState({ user: null, loading: false });
      }
    } catch (error) {
      console.error('Session check error:', error);
      clearAuthData();
      setState({ user: null, loading: false });
    }
  }, [setState]);

  useEffect(() => {
    checkUserSession();
    // Set up the unauthorized callback for axios interceptor
    setUnauthorizedCallback(refreshAccessToken);

    // Cleanup on unmount
    return () => {
      setUnauthorizedCallback(null);
    };
  }, []);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user
        ? {
            ...state.user,
            role: state.user?.role ?? 'admin',
          }
        : null,
      checkUserSession,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
    }),
    [checkUserSession, state.user, status]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
