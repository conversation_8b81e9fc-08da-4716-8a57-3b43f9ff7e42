import { useCallback, useEffect, useMemo } from 'react';

import { useSetState } from 'src/hooks/use-set-state';

import axios, { endpoints, setUnauthorizedCallback } from 'src/utils/axios';

import { AuthContext } from '../auth-context';
import { refreshAccessToken } from './action';
import { navigateToSignIn } from './utils';

import type { AuthState } from '../../types';

// ----------------------------------------------------------------------

/**
 * NOTE:
 * We only build demo at basic level.
 * Customer will need to do some extra handling yourself if you want to extend the logic and other features...
 */

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const { state, setState } = useSetState<AuthState>({
    user: null,
    loading: true,
  });

  const checkUserSession = useCallback(async () => {
    try {
      // Try to get user data using refresh token cookie
      const res = await axios.get(endpoints.user.me);
      const user = res.data;
      setState({ user, loading: false });
    } catch (error) {
      console.error('Session check error:', error);
      // If user.me fails, user is not authenticated
      setState({ user: null, loading: false });
    }
  }, [setState]);

  useEffect(() => {
    checkUserSession();

    // Set up the unauthorized callback for axios interceptor
    setUnauthorizedCallback(async () => {
      try {
        const newAccessToken = await refreshAccessToken();
        if (newAccessToken) {
          // Update the user state with the new token
          const res = await axios.get(endpoints.user.me);
          const user = res.data;
          setState({ user, loading: false });
        }
        return newAccessToken;
      } catch (error) {
        console.error('Token refresh failed in callback:', error);
        // Navigate to sign in if refresh fails
        navigateToSignIn();
        setState({ user: null, loading: false });
        return null;
      }
    });

    // Cleanup on unmount
    return () => {
      setUnauthorizedCallback(null);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ----------------------------------------------------------------------

  const checkAuthenticated = state.user ? 'authenticated' : 'unauthenticated';

  const status = state.loading ? 'loading' : checkAuthenticated;

  const memoizedValue = useMemo(
    () => ({
      user: state.user
        ? {
            ...state.user,
            role: state.user?.role ?? 'admin',
          }
        : null,
      checkUserSession,
      loading: status === 'loading',
      authenticated: status === 'authenticated',
      unauthenticated: status === 'unauthenticated',
    }),
    [checkUserSession, state.user, status]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
