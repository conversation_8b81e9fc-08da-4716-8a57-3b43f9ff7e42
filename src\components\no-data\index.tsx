import { Stack, SxProps, TableCell, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import NoDataIcon from '../../assets/image/no-data';

export const NoData = ({
  sx,
  label = 'components.noData.noDataAvailable',
  isTableView = false,
  showIcon = true,
}: {
  sx?: SxProps;
  label?: any;
  isTableView?: boolean;
  showIcon?: boolean;
}) => {
  const { t } = useTranslation();

  const Content = (
    <Stack display="flex" justifyContent="center" gap='4px' alignItems="center" sx={{ ...sx, py: 2 }}>
      {showIcon && <NoDataIcon />}
      {label && (
        <>
        <Typography variant="h3" color="text.primary">
          {t(label)}
        </Typography>
        <Typography variant="body1" color="text.primary">
          {t('components.noData.listAvailableMessage', { item: t(label) })}
        </Typography>
        </>
        )}
    </Stack>
  );

  return isTableView ? <TableCell colSpan={12}>{Content}</TableCell> : Content;
};

export default NoData;
