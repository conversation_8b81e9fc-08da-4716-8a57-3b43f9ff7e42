import { useState, useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { paths } from 'src/routes/paths';
import { useTheme } from '@mui/material';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { useTemplatesApi } from 'src/services/api/use-templates-api';
// import {
//   useTemplatesTeamsApi,
//   TEAM_MODEL_OPTIONS,
//   TEAM_TYPE_OPTIONS,
// } from 'src/services/api/use-templates-teams-api';
import { useNavigate } from 'react-router';
import { UserTeam } from 'src/services/api/use-my-teams-api';
import {
  TEAM_TYPE_OPTIONS,
  TeamEdge,
  TeamFormValues,
  TeamTeamplatesType,
  useTeamTeamplatessApi,
} from 'src/services/api/use-teams-api';
import { MODEL_VALUES } from 'src/constants/constants';
// import { TeamFormValues, EnhancedTemplateTeam, TeamEdge } from '../view/use-teams-view';

// Edge schema for flow builder
const edgeSchema = z.object({
  id: z.string(),
  source: z.number(),
  dest: z.number(),
  type: z.literal('plain'),
  label: z.string(),
  description: z.string(),
});

// Form validation schema
const teamSchema = z.object({
  name: z.string().min(1, 'validation.nameRequired'),
  description: z.string().min(1, 'validation.descriptionRequired'),
  type: z.enum(['AUTO', 'MANUAL'], { required_error: 'validation.typeRequired' }),
  categoryId: z.number().min(1, 'validation.categoryRequired'),
  model: z.enum(MODEL_VALUES).optional(),

  templatesIds: z.array(z.number()).min(1, 'validation.atLeastOneTemplate'),
  status: z.enum(['ACTIVE', 'DISABLED'], { required_error: 'validation.statusRequired' }),
  edges: z.array(edgeSchema).optional(),
  selectedTemplates: z.array(z.any()).optional(), // Store full template data for flow builder
});

// Hook props interface
interface UseTeamFormProps {
  team: TeamTeamplatesType | null;
  initialTeamType?: 'AUTO' | 'MANUAL';
}

export function useTeamForm({ team, initialTeamType }: UseTeamFormProps) {
  const theme = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const navigate = useNavigate();

  // State for selected templates
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);

  // State for edges (flow builder)
  const [edges, setEdges] = useState<TeamEdge[]>([]);

  // API hooks
  const { useCreateTeamTeamplates, useUpdateTeamTeamplates } = useTeamTeamplatessApi();
  const { useGetCategories } = useCategoriesApi();
  const { useGetTemplates } = useTemplatesApi();

  const { mutate: createTeam, isPending: isCreating } = useCreateTeamTeamplates();
  const { mutate: updateTeam, isPending: isUpdating } = useUpdateTeamTeamplates(
    team?.id?.toString() || ''
  );
  const { data: categoriesResponse } = useGetCategories();
  const { data: templatesResponse } = useGetTemplates();

  const categories = categoriesResponse?.categories || [];
  const templates = templatesResponse?.templates || [];
  const isEditing = Boolean(team);

  // Form setup
  const methods = useForm<TeamFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
      type: initialTeamType || ('AUTO' as const),
      categoryId: 0,
      model: 'GPT_4O_MINI' as const,
      templatesIds: [],
      status: 'ACTIVE',
      edges: [],
      selectedTemplates: [],
    },
  });

  // Get current form values to determine team type
  const currentFormValues = methods.watch();
  const isManualType = currentFormValues.type === 'MANUAL';

  // Define steps similar to agent form
  const steps = useMemo(() => {
    const baseSteps = [
      {
        label: 'Team Info',
        fields: ['name', 'description', 'status'],
      },
      {
        label: 'Category',
        fields: ['categoryId'],
      },
      {
        label: 'Agent',
        fields: ['templatesIds'],
      },
    ];

    // Add Flow Builder step for MANUAL type
    if (isManualType) {
      baseSteps.push({
        label: 'Flow Builder',
        fields: ['edges'],
      });
    }

    // Add Model step at the end
    if (!isManualType) {
      baseSteps.push({
        label: 'Model',
        fields: ['model'],
      });
    }

    return baseSteps;
  }, [isManualType]);

  const {
    handleSubmit,
    trigger,
    setValue,
    reset,
    formState: { isSubmitting },
  } = methods;

  // Reset form when team changes
  useEffect(() => {
    if (team) {
      console.log('team', team);
      // Pre-fill form with team data for editing
      const templateTeam = team;

      // For now, we'll handle edges as empty array since UserTeam doesn't have edges
      const teamEdges: TeamEdge[] = team?.edges ? team?.edges : [];
      const templatesIds = team?.templatesInTeam?.map((item) => item?.template?.id);

      reset({
        name: templateTeam?.name || '',
        description: templateTeam?.description || '',
        type: templateTeam?.type as 'AUTO' | 'MANUAL',
        categoryId: templateTeam?.categoryId || 0,
        model: (templateTeam?.model as TeamFormValues['model']) || 'GPT_4O_MINI',
        templatesIds, // Will be populated from templates API
        status: templateTeam?.status === 'ACTIVE' ? 'ACTIVE' : 'DISABLED',
        edges: teamEdges,
        selectedTemplates: [],
      });

      setSelectedTemplates(templatesIds);
      setEdges(teamEdges);
    } else {
      // Reset to default values for creating new team
      reset({
        name: '',
        description: '',
        type: initialTeamType || ('AUTO' as const),
        categoryId: 0,
        model: 'GPT_4O_MINI' as const,
        templatesIds: [],
        status: 'ACTIVE', // Default status for new team
        edges: [],
        selectedTemplates: [],
      });
      setSelectedTemplates([]);
      setEdges([]);
    }
  }, [team, reset]);

  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = steps[activeStep];
    if (!currentStep) return;

    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as (keyof TeamFormValues)[]);

    if (isStepValid) {
      if (activeStep < steps.length - 1) {
        setActiveStep((prev) => prev + 1);
      }
    }
  }, [activeStep, trigger, steps]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  }, [activeStep]);

  // Handle template selection
  const handleTemplateToggle = useCallback(
    (templateId: number) => {
      setSelectedTemplates((prev) => {
        const newSelection = prev.includes(templateId)
          ? prev.filter((id) => id !== templateId)
          : [...prev, templateId];

        setValue('templatesIds', newSelection);
        return newSelection;
      });
    },
    [setValue]
  );

  // Handle edge management for flow builder
  const handleEdgesChange = useCallback(
    (newEdges: TeamEdge[]) => {
      setEdges(newEdges);
      setValue('edges', newEdges);
    },
    [setValue]
  );

  const values = methods.getValues();
  console.log('valuies', values);
  // console.log('eroros', methods?.formState?.errors);

  // Form submission
  const onFormSubmit = handleSubmit(async (data: TeamFormValues) => {
    console.log('data', data);
    try {
      // Prepare data for API
      const apiData = { ...data };

      // Remove model field for MANUAL type
      if (isManualType && apiData.model) {
        delete apiData.model;
      }
      if (!isManualType && apiData.edges) {
        delete apiData.edges;
      }

      // Remove selectedTemplates as it's not needed for API
      if ('selectedTemplates' in apiData) {
        delete (apiData as any).selectedTemplates;
      }

      if (isEditing) {
        // Update existing team

        updateTeam(apiData as any, {
          onSuccess: (data) => {
            navigate(paths.dashboard.teams.root);
          },
        });
      } else {
        // Create new team

        createTeam(apiData as any, {
          onSuccess: (data) => {
            navigate(paths.dashboard.teams.clone(data?.data?.id), {
              state: { name: data?.data?.name },
            });
          },
        });
      }

      // Navigate back to teams list
    } catch (error) {
      console.error('Form submission error:', error);
    }
  });

  return {
    // Form state
    methods,
    activeStep,
    isLoading: isCreating || isUpdating,
    isSubmitting,
    isCreating,
    isEditing,
    theme,

    // Navigation
    handleNext,
    handleBack,

    // Form submission
    onFormSubmit,

    // Data and filtering
    categories,
    templates,
    selectedTemplates,

    // Selection handlers
    handleTemplateToggle,
    handleEdgesChange,

    // Flow builder state
    edges,
    isManualType,

    // Constants and options
    steps,
    totalSteps: steps.length,
    isLastStep: activeStep === steps.length - 1,
    isFirstStep: activeStep === 0,
    TEAM_TYPE_OPTIONS,
  };
}
