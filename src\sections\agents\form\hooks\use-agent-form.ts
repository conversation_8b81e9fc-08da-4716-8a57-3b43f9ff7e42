import { zodResolver } from '@hookform/resolvers/zod';
import { useTheme } from '@mui/material';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import { LLM_MODEL_OPTIONS } from 'src/constants/constants';
import { paths } from 'src/routes/paths';
import { useCategoriesApi } from 'src/services/api/use-categories-api';
import { Template, useTemplatesApi } from 'src/services/api/use-templates-api';
import { useToolsApi } from 'src/services/api/use-tools-api';

import {
  agentFormSchema,
  AgentFormValues,
  FORM_STEPS,
  STATUS_OPTIONS,
  TYPE_OPTIONS,
} from '../config/agent-form-config';
import {
  filterItemsBySearch,
  formatCategoryOptions,
  formValuesToSubmission,
  getDefaultFormValues,
  getStepCompletionStatus,
  templateToFormValues,
} from '../utils/form-utils';

// ----------------------------------------------------------------------

interface UseAgentFormProps {
  agent: Template | null;
}

export function useAgentForm({ agent }: UseAgentFormProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);

  // API hooks
  const { useCreateTemplate, useUpdateTemplate } = useTemplatesApi();
  const { useGetCategories } = useCategoriesApi();
  const { useGetTools } = useToolsApi();

  const { mutate: createTemplate, isPending: isCreating } = useCreateTemplate();
  const { mutate: updateTemplate, isPending: isUpdating } = useUpdateTemplate(agent?.id!);
  const { data: categoriesResponse } = useGetCategories();
  const { data: toolsResponse } = useGetTools();

  const categories = categoriesResponse?.categories || [];
  const tools = toolsResponse?.tools || [];
  const isLoading = isCreating || isUpdating;
  const isEditing = Boolean(agent);

  // Search state
  const [toolSearchQuery, setToolSearchQuery] = useState('');
  const [categorySearchQuery, setCategorySearchQuery] = useState('');

  // Form setup
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentFormSchema),
    defaultValues: getDefaultFormValues(),
  });

  const {
    handleSubmit,
    trigger,
    reset,
    watch,
    setValue,
    formState: { isSubmitting, errors },
  } = methods;

  // Watch all form values for step completion checking
  const formValues = watch();

  // Reset form when agent changes
  useEffect(() => {
    if (agent) {
      // Use utility function to transform template to form values
      const formValues = templateToFormValues(agent);
      reset(formValues);
    } else {
      // Reset to default values for creating new agent
      reset(getDefaultFormValues());
    }
  }, [agent, reset]);

  // Search handlers
  const handleToolSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setToolSearchQuery(event.target.value);
  }, []);

  const handleCategorySearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategorySearchQuery(event.target.value);
  }, []);

  // Watch form values for selections
  const selectedTools = watch('toolsId') || [];
  const selectedModel = watch('model');

  // Filter data based on search queries
  const filteredTools = filterItemsBySearch(tools, toolSearchQuery);
  const filteredCategories = formatCategoryOptions(categories, categorySearchQuery);
  const filteredModels = [...LLM_MODEL_OPTIONS];

  // Selection handlers

  const handleModelSelect = useCallback(
    (
      modelValue:
        | 'GPT_4O_MINI'
        | 'GPT_4O'
        | 'CLAUDE_3_7_SONNET'
        | 'GEMINI_2_0_FLASH'
        | 'GEMINI_1_5_FLASH'
    ) => {
      setValue('model', modelValue);
    },
    [setValue]
  );
  // Handle tool selection
  const handleToolToggle = (toolId: number) => {
    const currentTools = [...selectedTools];
    const toolIndex = currentTools.indexOf(toolId);

    if (toolIndex === -1) {
      // Add the tool
      currentTools.push(toolId);
    } else {
      // Remove the tool
      currentTools.splice(toolIndex, 1);
    }

    setValue('toolsId', currentTools);
  };
  // Navigation handlers
  const handleNext = useCallback(async () => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return;

    // Validate current step fields
    const isStepValid = await trigger(currentStep.fields as any);

    if (isStepValid) {
      if (activeStep < FORM_STEPS.length - 1) {
        setActiveStep((prev) => prev + 1);
      }
    }
  }, [activeStep, trigger]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  }, [activeStep]);

  const handleStepClick = useCallback(
    async (stepIndex: number) => {
      // Validate all previous steps before allowing navigation
      let canNavigate = true;

      for (let i = 0; i < stepIndex; i += 1) {
        const step = FORM_STEPS[i];
        // eslint-disable-next-line no-await-in-loop
        const isStepValid = await trigger(step.fields as any);
        if (!isStepValid) {
          canNavigate = false;
          break;
        }
      }

      if (canNavigate) {
        setActiveStep(stepIndex);
      }
    },
    [trigger]
  );

  // Form submission
  // Form submission
  const onFormSubmit = handleSubmit(async (data: AgentFormValues) => {
    try {
      const submissionData = formValuesToSubmission(data);

      // Clean toolsId based on editing/creating
      let cleanedObj;
      if (isEditing) {
        // عند التعديل: احذف toolsId كلياً
        const { toolsId, ...rest } = submissionData;
        cleanedObj = rest;
        updateTemplate(cleanedObj as any, {
          onSuccess: (data) => {
            navigate(paths.dashboard.agents.root);
          },
        });
      } else {
        // عند الإنشاء: خليه بس إذا مو فاضي
        const { toolsId, ...rest } = submissionData;
        cleanedObj = toolsId?.length ? { ...rest, toolsId } : rest;
        createTemplate(cleanedObj as any, {
          onSuccess: (data) => {
            console.log('data', data);
            navigate(paths.dashboard.agents.clone(data?.data?.id!), {
              state: { name: data?.data?.name },
            });
          },
        });
      }
    } catch (error) {
      console.error('Form submission error:', error);
    }
  });

  // Helper functions
  const getCurrentStepErrors = useCallback(() => {
    const currentStep = FORM_STEPS[activeStep];
    if (!currentStep) return [];

    return currentStep.fields.filter((field) => errors[field]);
  }, [activeStep, errors]);

  const isStepCompleted = useCallback(
    (stepIndex: number) => {
      const step = FORM_STEPS[stepIndex];
      if (!step) return false;

      const { isCompleted } = getStepCompletionStatus(step.fields, errors, formValues);
      return isCompleted;
    },
    [errors, formValues]
  );

  const canProceedToNext = useCallback(() => {
    return getCurrentStepErrors().length === 0;
  }, [getCurrentStepErrors]);

  return {
    // Form state
    methods,
    activeStep,
    isLoading,
    isSubmitting,
    isEditing,
    theme,

    // Navigation
    handleNext,
    handleBack,
    handleStepClick,

    // Form submission
    onFormSubmit,

    // Helper functions
    getCurrentStepErrors,
    isStepCompleted,
    canProceedToNext,

    // Data and filtering
    categories: filteredCategories,
    tools: filteredTools,
    availableTools: filteredTools,
    options: filteredCategories,

    // Search functionality
    toolSearchQuery,
    categorySearchQuery,
    handleToolSearchChange,
    handleCategorySearchChange,

    // Selection state
    selectedTools,
    selectedModel,

    // Selection handlers
    handleModelSelect,

    // Filtered data
    LLM_MODEL: filteredModels,

    // Constants and options
    steps: FORM_STEPS,
    totalSteps: FORM_STEPS.length,
    isLastStep: activeStep === FORM_STEPS.length - 1,
    isFirstStep: activeStep === 0,
    TYPE_OPTIONS,
    STATUS_OPTIONS,
  };
}

export default useAgentForm;
