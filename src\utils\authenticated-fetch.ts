import { refreshAccessToken } from 'src/auth/context/jwt';
import { paths } from 'src/routes/paths';
import axios from './axios';

// this is for refresh-token and take the accessToken from the axios header and when the task faild because of the finished the accessToken to re-verified
// Best for streaming → fetch (native, clean, no hacks).

// Best for all other requests → axios (with interceptors).
// here i use the fetch just for streaming

export const authenticatedFetch = async (
  url: string,
  options: RequestInit,
  retryCount = 0
): Promise<Response> => {
  // Get current authorization header from axios defaults
  const authHeader = axios.defaults.headers.common.Authorization;

  // Convert authHeader to string
  const authHeaderString = typeof authHeader === 'string' ? authHeader : '';

  const fetchOptions = {
    ...options,
    headers: {
      ...options.headers,
      Authorization: authHeaderString,
    },
  };

  const response = await fetch(url, fetchOptions);

  // If we get 401 and haven't retried yet, try to refresh token
  if (response.status === 401 && retryCount === 0) {
    try {
      const newAccessToken = await refreshAccessToken();
      if (newAccessToken) {
        // Retry with new token
        return await authenticatedFetch(url, options, retryCount + 1);
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    // If refresh failed, redirect to sign in
    window.location.href = paths.auth.jwt.signIn;
    throw new Error('Authentication failed');
  }

  return response;
};
