import axios from 'axios';

import { CONFIG } from 'src/config-global';
import { localStorageGetItem } from './storage-available';

// ----------------------------------------------------------------------

// Callback for handling 401 errors - to be set by auth context
let onUnauthorizedCallback: (() => Promise<string | null>) | null = null;

export const setUnauthorizedCallback = (callback: (() => Promise<string | null>) | null) => {
  onUnauthorizedCallback = callback;
};

const axiosInstance = axios.create({
  baseURL: CONFIG.site.WorkforcesServerUrl,
  withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
  // Only set language header, Authorization header is managed by setSession
  config.headers['accept-language'] = localStorageGetItem('i18nextLng');

  return config;
});
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log('error', error);
    const originalRequest = error.config;

    // Check if we're on an auth page (login/signup/etc.)
    const isOnAuthPage =
      typeof window !== 'undefined' && window.location.pathname.startsWith('/auth');

    if (
      error.response &&
      error.response.status === 401 &&
      originalRequest &&
      !originalRequest.url?.includes(endpoints.auth.refreshToken) &&
      !originalRequest._retry && // Prevent infinite retry loops
      !isOnAuthPage // Don't trigger refresh token on auth pages
    ) {
      originalRequest._retry = true;

      try {
        // Call the refresh token callback
        if (onUnauthorizedCallback) {
          const newAccessToken = await onUnauthorizedCallback();

          if (newAccessToken) {
            // Update the original request with the new token
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
            // Retry the original request
            return await axiosInstance(originalRequest);
          }
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        // If refresh fails, let the error fall through to logout
      }
    }

    // Always throw the backend error message (or fallback)
    const errorMessage =
      error.response?.data?.message || error.response?.statusText || 'Something went wrong!';

    return Promise.reject(new Error(errorMessage));
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/basic',
    signUp: '/auth/register',
    signOut: '/auth/logout',
    refreshToken: '/auth/refresh-token',
    forgetPassword: '/auth/request-password-change',
    resetPassword: '/auth/reset-password',
    exchangeOAuthCode: '/auth/login/exchange-code',
    verifyEmail: '/auth/verify-email',
  },
  user: {
    me: '/users/me',
  },
};
