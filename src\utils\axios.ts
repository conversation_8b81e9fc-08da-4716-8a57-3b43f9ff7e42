import axios from 'axios';

import { CONFIG } from 'src/config-global';
import { localStorageGetItem, localStorageSetItem } from './storage-available';

// ----------------------------------------------------------------------

// Callback for handling 401 errors - to be set by auth context
let onUnauthorizedCallback: (() => Promise<string | null>) | null = null;

// Token refresh state management
let isRefreshing = false;
let refreshTimeout: NodeJS.Timeout | null = null;
let failedQueue: Array<{
  resolve: (value: string | null) => void;
  reject: (error: any) => void;
}> = [];

// this function to prevend duplicate the request multi time in the same time especialy refreshToken
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

const resetRefreshState = () => {
  isRefreshing = false;
  if (refreshTimeout) {
    clearTimeout(refreshTimeout);
    refreshTimeout = null;
  }
};

export const setUnauthorizedCallback = (callback: (() => Promise<string | null>) | null) => {
  onUnauthorizedCallback = callback;
};

const axiosInstance = axios.create({
  baseURL: CONFIG.site.WorkforcesServerUrl,
  withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
  // Only set language header, Authorization header is managed by setSession
  config.headers['accept-language'] = localStorageGetItem('i18nextLng');

  return config;
});
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log('error', error);
    const originalRequest = error.config;

    // Check if we're on an auth page (login/signup/etc.)
    const isOnAuthPage =
      typeof window !== 'undefined' && window.location.pathname.startsWith('/auth');

    if (
      error.response &&
      error.response.status === 401 &&
      originalRequest &&
      !originalRequest.url?.includes(endpoints.auth.refreshToken) &&
      !originalRequest._retry && // Prevent infinite retry loops
      !isOnAuthPage // Don't trigger refresh token on auth pages
    ) {
      // If we're already refreshing, queue this request
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (token) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return axiosInstance(originalRequest);
            }
            return Promise.reject(error);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      // Set a timeout to prevent infinite waiting
      refreshTimeout = setTimeout(() => {
        console.error('Token refresh timeout');
        processQueue(new Error('Token refresh timeout'), null);
        resetRefreshState();
      }, 10000); // 10 seconds timeout

      try {
        // Call the refresh token callback
        if (onUnauthorizedCallback) {
          const newAccessToken = await onUnauthorizedCallback();

          if (newAccessToken) {
            // Process the queue with the new token
            processQueue(null, newAccessToken);
            // Update the original request with the new token
            originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
            // Retry the original request
            return await axiosInstance(originalRequest);
          }
          processQueue(new Error('Token refresh failed'), null);
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        // Process queue with error
        processQueue(refreshError, null);
      } finally {
        resetRefreshState();
      }
    }

    // Always throw the backend error message (or fallback)
    const errorMessage =
      error.response?.data?.message || error.response?.statusText || 'Something went wrong!';

    return Promise.reject(new Error(errorMessage));
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/basic',
    signUp: '/auth/register',
    signOut: '/auth/logout',
    refreshToken: '/auth/refresh-token',
    forgetPassword: '/auth/request-password-change',
    resetPassword: '/auth/reset-password',
    exchangeOAuthCode: '/auth/login/exchange-code',
    verifyEmail: '/auth/verify-email',
  },
  user: {
    me: '/users/me',
  },
};
