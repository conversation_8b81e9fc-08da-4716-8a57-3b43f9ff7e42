import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';

import { varAlpha } from 'src/theme/styles';
import { DashboardContent } from 'src/layouts/dashboard';
import { App<PERSON><PERSON>on, AppContainer } from 'src/components/common';

// ----------------------------------------------------------------------

type Props = {
  title?: string;
};

export function BlankView({ title }: Props) {
  const { t } = useTranslation();
  return (
    // <DashboardContent maxWidth="xl">
    <AppContainer>
      <Typography variant="h4"> {title || t('ui.blank')} </Typography>
      <Typography>{t('ui.hello')}</Typography>

      <Box
        sx={{
          mt: 5,
          width: 1,
          height: 320,
          borderRadius: 2,
          bgcolor: (theme) => varAlpha(theme.vars.palette.grey['500Channel'], 0.04),
          border: (theme) => `dashed 1px ${theme.vars.palette.divider}`,
        }}
      />
    </AppContainer>

    // </DashboardContent>
  );
}
