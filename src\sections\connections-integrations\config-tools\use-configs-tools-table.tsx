import { Box, Typography } from '@mui/material';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import LongMenu from 'src/components/long-menu';
import { useTable } from 'src/components/table';
import { AppTablePropsType } from 'src/components/table/app-table/types';
import { useBoolean } from 'src/hooks/use-boolean';
import { useDebounce } from 'src/hooks/use-debounce';
import { ToolConfigsType, useToolConfigApi } from 'src/services/api/use-toolconfig-api';
import { Tool, useToolsApi } from 'src/services/api/use-tools-api';
import { fDate } from 'src/utils/format-time';

// Local import should be last

// Define the tool config item type based on the API response
type ToolConfigItem = ToolConfigsType['toolsConfigs'][0];

export const useConfigsToolsTable = () => {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedToolId, setSelectedToolId] = useState<string>('');

  const [deleteToolId, setDeleteToolId] = useState<number>();
  // Debounce search query for API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Table configuration
  const table = useTable();
  const openPopUpdelete = useBoolean();
  // Edit dialog state
  const openEditDialog = useBoolean();
  const [editToolConfig, setEditToolConfig] = useState<ToolConfigItem | null>(null);
  const [editName, setEditName] = useState('');

  const { t } = useTranslation();
  const handleOpenPopUpConfirmation = (id: number) => {
    setDeleteToolId(id);
    openPopUpdelete.onTrue();
  };

  // API hooks
  const { useGetTools } = useToolsApi();
  const { useGetToolConfigs, useDeleteCToolConfig, useUpdateToolConfig } = useToolConfigApi();
  const { mutate: updateToolConfig, isPending: isPendingUpdate } = useUpdateToolConfig(
    editToolConfig?.id ?? ''
  );

  // Fetch tools for the filter dropdown
  const { data: toolsData, isLoading: isLoadingTools } = useGetTools();
  const { mutate: deleteToolConfig, isPending: isPendingDelete } = useDeleteCToolConfig();

  // Fetch tool configs with filters (✅ add take + skip)
  const { data: toolConfigsData, isLoading: isLoadingConfigs } = useGetToolConfigs({
    toolId: selectedToolId === 'All' ? '' : selectedToolId,
    name: debouncedSearchQuery || undefined,
    take: table.rowsPerPage,
    skip: table.rowsPerPage * (table.page - 1),
  });

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    table.setPage(1);
    table.setRowsPerPage(10);
    setSearchQuery(e.target.value);
  };

  // Handle tool filter change
  const handleToolFilterChange = (toolId: string) => {
    setSelectedToolId(toolId);
    table.setPage(1);
    table.setRowsPerPage(10);
  };

  // Prepare tools options for the filter dropdown
  const toolsOptions = useMemo(() => {
    if (!toolsData?.tools) return [];
    return [
      { label: 'All Tools', value: 'All' },
      ...toolsData.tools.map((tool: Tool) => ({
        label: tool.name,
        value: tool.id.toString(),
      })),
    ];
  }, [toolsData]);

  const handleDeleteToolConfig = () => {
    if (deleteToolId)
      deleteToolConfig(deleteToolId, {
        onSuccess: () => {
          openPopUpdelete.onFalse();
        },
      });
  };

  // Edit handlers
  const handleOpenEditDialog = (toolConfig: ToolConfigItem) => {
    setEditToolConfig(toolConfig);
    setEditName(toolConfig.name);
    openEditDialog.onTrue();
  };

  const handleCloseEditDialog = () => {
    openEditDialog.onFalse();
    setEditToolConfig(null);
    setEditName('');
  };

  const handleSubmitEdit = () => {
    if (!editToolConfig) return;
    updateToolConfig(
      { toolId: editToolConfig.toolId, name: editName },
      {
        onSuccess: () => {
          handleCloseEditDialog();
        },
      }
    );
  };

  const getMenuOptions = (toolConfig: ToolConfigItem) => [
    {
      label: 'delete',
      icon: 'eva:trash-2-outline',
      onClick: () => handleOpenPopUpConfirmation(toolConfig?.id),
      color: 'error.main',
    },
    {
      label: 'edit',
      icon: 'eva:edit-fill',
      onClick: () => handleOpenEditDialog(toolConfig),
      color: 'inherit',
    },
  ];

  // Table head labels
  const headLabels = [
    { id: 'name', label: 'Name' },
    { id: 'tool', label: 'Tool' },
    { id: 'createdAt', label: 'Date Created' },
    { id: 'agentCount', label: 'Agents Count' },
    { id: 'action', label: 'Action' },
  ];

  // Table columns configuration
  const columns: AppTablePropsType<ToolConfigItem>['columns'] = [
    {
      name: 'name',
      PreviewComponent: ({ name, tool }) => (
        <Box display="flex" alignItems="center" gap={1.5}>
          <Iconify icon={tool?.icon} />
          <Typography variant="body2" fontWeight={500} color="text.primary">
            {name}
          </Typography>
        </Box>
      ),
    },
    {
      name: 'tool',
      PreviewComponent: ({ tool }) => (
        <Typography variant="body2" color="text.secondary">
          {tool?.name}
        </Typography>
      ),
    },
    {
      name: 'createdAt',
      PreviewComponent: ({ createdAt }) => (
        <Typography variant="body2" color="text.secondary">
          {fDate(createdAt)}
        </Typography>
      ),
    },
    {
      name: '_count',
      // ✅ Fix unsafe optional chaining in arithmetic
      PreviewComponent: ({ _count }) => (
        <Typography variant="body2" color="text.secondary">
          {(_count?.agentTools ?? 0) + (_count?.teamTools ?? 0)} Agents
        </Typography>
      ),
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (toolConfig) => <LongMenu options={getMenuOptions(toolConfig)} />,
    },
  ];

  // Search props for AppTable
  const searchProps = {
    searchValue: searchQuery,
    setQuery: () => {}, // Not used in this implementation
    handleSearch,
  };

  // Filter props for AppTable
  const filtersProp = {
    reset: () => {
      setSelectedToolId('');
      setSearchQuery('');
    },
    openFilters: { value: false, onTrue: () => {}, onFalse: () => {} },
    filters: [
      {
        title: 'Tool',
        type: 'select' as const,
        value: selectedToolId,
        options: toolsOptions,
        onChange: (e: any) => handleToolFilterChange(e.target.value),
      },
    ],
    areFiltersApplied: selectedToolId !== '',
  };

  return {
    // Data
    toolConfigs: toolConfigsData?.toolsConfigs || [],
    totalCount: toolConfigsData?.total,

    // Loading states
    isLoading: isLoadingConfigs || isLoadingTools,

    // Table configuration
    table,
    headLabels,
    columns,

    // Search and filters
    searchProps,
    filtersProp,
    searchQuery,
    selectedToolId,

    // Tools data for filter
    toolsOptions,

    // Handlers
    handleSearch,
    handleToolFilterChange,
    openPopUpdelete,
    handleDeleteToolConfig,
    isPendingDelete,

    // Edit dialog
    openEditDialog,
    editName,
    setEditName,
    handleSubmitEdit,
    handleCloseEditDialog,
    isPendingUpdate,
    editToolConfig,
    t,
  };
};
