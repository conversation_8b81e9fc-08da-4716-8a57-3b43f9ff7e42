import React from 'react';
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { AppButton } from 'src/components/common';
import { useConfigsToolsTable } from './use-configs-tools-table';

const ConfigToolsTable = () => {
  const {
    toolConfigs,
    totalCount,
    isLoading,
    table,
    headLabels,
    columns,
    searchQuery,
    selectedToolId,
    toolsOptions,
    handleSearch,
    handleToolFilterChange,
    openPopUpdelete,
    handleDeleteToolConfig,
    isPendingDelete,
    // Edit dialog props
    openEditDialog,
    editName,
    setEditName,
    handleSubmitEdit,
    handleCloseEditDialog,
    isPendingUpdate,
    editToolConfig,
    t,
  } = useConfigsToolsTable();

  return (
    <Box>
      {/* Header */}

      {/* Search and Filter Controls */}

      {/* Search Field */}
      <TextField
        placeholder="Search"
        value={searchQuery}
        onChange={handleSearch}
        sx={{
          width: '100%',
          backgroundColor: (theme) => theme?.palette.background.neutral,
          '& .MuiOutlinedInput-root': {},
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Iconify icon="eva:search-fill" width={20} sx={{ color: 'text.disabled' }} />
            </InputAdornment>
          ),
        }}
      />
      {/* Tool Filter */}
      <FormControl sx={{ minWidth: 100, mt: '24px' }}>
        <InputLabel id="tool-filter-label">Tool</InputLabel>
        <Select
          labelId="tool-filter-label"
          value={selectedToolId}
          label="Tool"
          onChange={(e) => handleToolFilterChange(e.target.value)}
          sx={{ mb: '24px' }}
        >
          {toolsOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {/* Table */}
      <AppTable
        headLabels={headLabels}
        data={toolConfigs}
        columns={columns}
        dataCount={totalCount!}
        table={table}
        isLoading={isLoading}
        noDataLabel="No tool configurations found"
        sx={{
          boxShadow: 'none',
          border: '1px solid',
          borderColor: 'divider',
          '& .MuiTableCell-head': {
            bgcolor: 'transparent',
          },
          '& .MuiTableCell-root': {
            borderBottom: '1px solid',
            borderBottomColor: 'divider',
          },
        }}
      />

      {/* delete my team template */}
      <ConfirmDialog
        open={openPopUpdelete.value}
        onClose={() => openPopUpdelete.onFalse()}
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: -1 }}>
            <Iconify icon="solar:trash-bin-trash-bold" width={44} color="error.main" />
            <Typography variant="h6">Delete template?</Typography>
          </Box>
        }
        content={
          <Typography sx={{ color: 'rgba(15, 14, 17, 0.65)', textAlign: 'start' }} variant="body2">
            Are you sure you want to delete this tool config?{' '}
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', justifyContent: 'start', gap: 1 }}>
            <Divider />
            <AppButton
              label={t('components.chat.cancel')}
              variant="outlined"
              sx={{ backgroundColor: 'white' }}
              onClick={() => openPopUpdelete.onFalse()}
            />
            <AppButton
              isLoading={isPendingDelete}
              label="Delete"
              variant="contained"
              color="error"
              onClick={handleDeleteToolConfig}
            />
          </Box>
        }
      />

      {/* Edit Tool Config Dialog */}
      <Dialog open={openEditDialog.value} onClose={handleCloseEditDialog} fullWidth maxWidth="sm">
        <Box sx={{ backgroundColor: '#EBEAEB' }}>
          <DialogTitle>Edit tool config</DialogTitle>
          <DialogContent>
            <Stack spacing={2} sx={{ mt: 1 }}>
              <TextField
                label="Name"
                sx={{ mb: '24px' }}
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                fullWidth
                autoFocus
                placeholder={editToolConfig?.name || 'Enter name'}
              />
            </Stack>
          </DialogContent>
          <DialogActions
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              pb: 2,
              backgroundColor: '#E3E1E4',
              borderTop: '1px solid #C9C5D1',
            }}
          >
            <AppButton
              label="Cancel"
              fullWidth={false}
              variant="outlined"
              color="inherit"
              sx={{ bgcolor: 'white' }}
              onClick={handleCloseEditDialog}
            />
            <AppButton
              label="Save"
              fullWidth={false}
              variant="contained"
              isLoading={isPendingUpdate}
              onClick={handleSubmitEdit}
            />
          </DialogActions>
        </Box>
      </Dialog>
    </Box>
  );
};

export default ConfigToolsTable;
