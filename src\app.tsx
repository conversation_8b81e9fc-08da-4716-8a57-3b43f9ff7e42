import { useEffect } from 'react';

import 'src/global.css';
import './i18n'; // Import i18n configuration

// ----------------------------------------------------------------------

import { Router } from 'src/routes/sections';

import { useScrollToTop } from 'src/hooks/use-scroll-to-top';

import { ThemeProvider } from 'src/theme/theme-provider';

import { MotionLazy } from 'src/components/animate/motion-lazy';
import { ProgressBar } from 'src/components/progress-bar';
import { defaultSettings, SettingsDrawer, SettingsProvider } from 'src/components/settings';
import { AccountMenuProvider } from 'src/contexts/account-menu-context';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider, refreshAccessToken } from 'src/auth/context/jwt';
import SnackbarProvider from './components/snackbar/snackbar-provider';
import { DeveloperModeProvider } from './contexts/developer-mode-context';

// ----------------------------------------------------------------------

export default function App() {
  useEffect(() => {
    refreshAccessToken?.();
  }, []);
  useScrollToTop();
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false,
      },
    },
  });

  return (
    <DeveloperModeProvider>
      <AuthProvider>
        <QueryClientProvider client={queryClient}>
          <SettingsProvider settings={defaultSettings}>
            <ThemeProvider>
              <AccountMenuProvider>
                <MotionLazy>
                  <SnackbarProvider>
                    <ProgressBar />
                    <SettingsDrawer />
                    <Router />
                  </SnackbarProvider>
                </MotionLazy>
              </AccountMenuProvider>
            </ThemeProvider>
          </SettingsProvider>
        </QueryClientProvider>
      </AuthProvider>
    </DeveloperModeProvider>
  );
}
