import { useTranslation } from 'react-i18next';
import { FORM_FIELD_CONFIG } from 'src/sections/agents/form/config/agent-form-config';

/**
 * Hook to get translated form field configurations
 * This helps translate form labels, placeholders, and helper text
 */
export const useFormTranslations = () => {
  const { t } = useTranslation();

  // Translate form field configurations
  const getTranslatedFieldConfig = (fieldKey: keyof typeof FORM_FIELD_CONFIG) => {
    const config = FORM_FIELD_CONFIG[fieldKey];
    if (!config) return null;

    return {
      ...config,
      label: t(config.label),
      placeholder: t(config.placeholder),
      helperText: t(config.helperText),
    };
  };

  // Get all translated field configurations
  const translatedFields = {
    name: getTranslatedFieldConfig('name'),
    description: getTranslatedFieldConfig('description'),
    systemMessage: getTranslatedFieldConfig('systemMessage'),
  };

  return {
    t,
    getTranslatedFieldConfig,
    translatedFields,
  };
};
