import { useState, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  IconButton,
  LinearProgress,
  Stack,
  Divider,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { background } from 'src/theme/core';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'completed' | 'error';
  progress: number;
}

type FileUploadDialogProps = {
  open: boolean;
  onClose: () => void;
};

const ACCEPTED_FILE_TYPES = ['doc', 'docx', 'pdf', 'txt', 'jpg', 'png'];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export default function FileUploadDialog({ open, onClose }: FileUploadDialogProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !ACCEPTED_FILE_TYPES.includes(fileExtension)) {
      return false;
    }
    if (file.size > MAX_FILE_SIZE) {
      return false;
    }
    return true;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const validFiles = Array.from(files).filter(validateFile);

    const newFiles: UploadedFile[] = validFiles.map((file) => ({
      id: Math.random().toString(36).substring(2, 11),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0,
    }));

    setUploadedFiles((prev) => [...prev, ...newFiles]);

    // Simulate upload for each file
    newFiles.forEach((uploadFile) => {
      simulateUpload(uploadFile.id);
    });
  };

  const simulateUpload = (fileId: string) => {
    const interval = setInterval(() => {
      setUploadedFiles((prev) =>
        prev.map((file) => {
          if (file.id === fileId) {
            const newProgress = Math.min(file.progress + 10, 100);
            return {
              ...file,
              progress: newProgress,
              status: newProgress === 100 ? 'completed' : 'uploading',
            };
          }
          return file;
        })
      );
    }, 200);

    // Clear interval when upload completes
    setTimeout(() => {
      clearInterval(interval);
    }, 2000);
  };

  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleDone = () => {
    setUploadedFiles([]);
    onClose();
  };

  const handleCancel = () => {
    setUploadedFiles([]);
    onClose();
  };

  const handleClearAll = () => {
    setUploadedFiles([]);
  };

  const completedFiles = uploadedFiles.filter((file) => file.status === 'completed');

  return (
    <Dialog
      open={open}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          bgcolor: 'background.paper',
          maxWidth: 440,
        },
      }}
    >
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
        multiple
        accept=".doc,.docx,.pdf,.txt,.jpg,.png"
      />

      <DialogTitle sx={{ position: 'relative', p: 3, pb: 2 }}>
        <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
          Upload File
        </Typography>

        <IconButton
          onClick={handleCancel}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Iconify icon="eva:close-fill" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3, py: 2 }}>
        {/* Upload Area */}
        <Box
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleUploadClick}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 200,
            // border: '2px dashed',
            // borderColor: isDragOver ? 'primary.main' : 'divider',
            borderRadius: 2,
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            mb: 3,
          }}
        >
          {/* Upload Icon */}
          <Box
            sx={{
              width: 64,
              height: 64,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 2,
            }}
          >
            <Iconify
              icon="eva:cloud-upload-outline"
              width={48}
              height={48}
              sx={{ color: 'text.secondary' }}
            />
          </Box>

          {/* Upload Text */}
          <Box display="flex">
            <Typography
              variant="body1"
              sx={{
                color: 'primary.main',
                fontWeight: 500,
                mb: 1,
                textDecoration: 'underline',
                cursor: 'pointer',
              }}
            >
              Upload a file
            </Typography>

            <Typography variant="body1" sx={{ color: 'inherit', mx: 1 }}>
              or drag and drop
            </Typography>
          </Box>

          <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center' }}>
            You can upload doc, docx, pdf, txt, jpg, png files.
            <br />
            Files can&apos;t be larger than 10 MB.
          </Typography>
        </Box>

        {/* File List */}
        {uploadedFiles.length > 0 && (
          <Box>
            {/* File Items */}
            <Stack
              spacing={1.5}
              sx={{
                p: 1,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                bgcolor: 'background.paper',
              }}
            >
              {uploadedFiles.map((file) => (
                <Box
                  key={file.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {/* File Info */}
                  <Box sx={{ flex: 1, minWidth: 0 , borderRight:'1px solid' ,borderColor:'divider'} }>
                    
                    <Stack direction='row'>
                      <Iconify
                        icon="eva:file-outline"
                        width={20}
                        height={20}
                        sx={{ mr:1}}
                        />
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 500,
                        color: 'text.primary',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                       {' '}
                      {file.name}
                    </Typography>
                        </Stack> 

                    {file.status === 'uploading' && (
                      <Box sx={{ mt: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                            {file.progress}% Uploading...
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={file.progress}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            bgcolor: 'action.hover',
                            '& .MuiLinearProgress-bar': {
                              bgcolor: 'primary.main',
                              borderRadius: 2,
                            },
                          }}
                        />
                      </Box>
                    )}

                    {file.status === 'completed' && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                        <Iconify
                          icon="eva:checkmark-circle-2-fill"
                          width={16}
                          height={16}
                          sx={{ color: 'success.main', mr: 0.5 }}
                        />
                        <Typography variant="caption" sx={{ color: 'success.main' }}>
                          Successfully uploaded
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  {/* Remove Button */}
                  <IconButton
                    size="small"
                    onClick={() => handleRemoveFile(file.id)}
                    sx={{
                      ml:1,
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'error.main',
                      },
                    }}
                  >
                    <Iconify icon="eva:trash-2-outline" width={20} height={20} />
                  </IconButton>
                </Box>
              ))}
            </Stack>
            {/* Files Header */}
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
            >
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {uploadedFiles.length} files selected
              </Typography>
              {completedFiles.length > 0 && (
                <Button
                  variant="text"
                  size="small"
                  onClick={handleClearAll}
                  sx={{
                    color: 'primary.main',
                    fontSize: '0.875rem',
                    textTransform: 'none',
                    p: 0,
                    minWidth: 'auto',
                  }}
                >
                  Clear all
                </Button>
              )}
            </Box>
          </Box>
        )}
      </DialogContent>
  <Divider/>
      <DialogActions sx={{ p: 3, justifyContent: 'flex-end', gap: 1,bgcolor:'divider' }}>
        <AppButton
          variant="outlined"
          onClick={handleCancel}
          label="Cancel"
          sx={{
            width: '20%',
            borderRadius: 1,
            borderColor: 'divider',
            color: 'text.primary',
            textTransform: 'none',
            px: 3,
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
        />

        {completedFiles.length !== 0 && (
          <AppButton
            variant="contained"
            onClick={handleDone}
            label="Done"
            sx={{
              width: '20%',
              bgcolor: 'primary.main',
              color: 'primary.contrastText',
              borderRadius: 1,
              textTransform: 'none',
              px: 3,
              '&:hover': {
                bgcolor: 'primary.dark',
              },
              '&.Mui-disabled': {
                bgcolor: 'action.disabledBackground',
                color: 'action.disabled',
              },
            }}
          />
        )}
      </DialogActions>
    </Dialog>
  );
}
