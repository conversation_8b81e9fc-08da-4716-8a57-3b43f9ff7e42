import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { DashboardLayout, DashboardLayoutNoSidebar } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';
import TeamTemplatesChat from 'src/sections/teams/chat/agents-chat';

import { AuthGuard } from 'src/auth/guard';
import { PermissionBasedGuard } from 'src/auth/guard/permission-based-guard';
import { paths } from '../paths';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('src/pages/dashboard/one'));

// agents pages
const AgentsPage = lazy(() => import('src/pages/dashboard/agents/agents-page'));
const TemplatesPage = lazy(() => import('src/pages/dashboard/agents/templates-page'));
const AgentsClonePage = lazy(() => import('src/pages/dashboard/agents/agents-clone-page'));
const ConfigureToolsPage = lazy(() => import('src/pages/dashboard/agents/configure-tools-page'));

// team templates pages
const MyTeamsTemplatesPage = lazy(() => import('src/pages/dashboard/teams/my-templates-page'));
const TemplatesTeamPage = lazy(() => import('src/pages/dashboard/teams/templates-page'));
const TeamTemplatessClonePage = lazy(() => import('src/pages/dashboard/teams/temlates-clone-page'));
const ConfigureteamToolsPage = lazy(
  () => import('src/pages/dashboard/agents/configure-tools-page')
);

const AgentsChatPage = lazy(() => import('src/pages/dashboard/agents/agents-chat-page'));
const ConnectionsIntegrationsPage = lazy(
  () => import('src/pages/dashboard/connections-integrations/connections-integrations-page')
);
const SettingsPage = lazy(() => import('src/pages/dashboard/settings/settings-page'));
const ScheduledTasksPage = lazy(
  () => import('src/pages/dashboard/scheduled-tasks/scheduled-tasks-page')
);

const CreateAgentPage = lazy(() => import('src/pages/dashboard/agents/create-agent-page'));

const CreateTeanTeamplatePage = lazy(() => import('src/pages/dashboard/teams/templates-form-page'));
// const PageTwo = lazy(() => import('src/pages/dashboard/two'));

// ----------------------------------------------------------------------

// to choose the redirect url to the team or agent clone
const agentOrTeam = localStorage.getItem('typeOfConfigureation');
const layoutContent = (
  <DashboardLayout>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayout>
);

const layoutContentNoSidebar = (
  <DashboardLayoutNoSidebar>
    <Suspense fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  </DashboardLayoutNoSidebar>
);

export const dashboardRoutes = [
  // Routes with sidebar
  {
    path: paths.dashboard.root,
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      // agents pages - protected by permissions
      {
        path: paths.dashboard.agents.root,
        element: (
          <PermissionBasedGuard permission={['get-agents', 'get-templates']}>
            <TemplatesPage />
          </PermissionBasedGuard>
        ),
      },
      {
        path: paths.dashboard.agents.agents,
        element: (
          <PermissionBasedGuard permission="get-agents">
            <AgentsPage />
          </PermissionBasedGuard>
        ),
      },
      {
        path: paths.dashboard.agents.templates,
        element: (
          <PermissionBasedGuard permission="get-templates">
            <TemplatesPage />
          </PermissionBasedGuard>
        ),
      },

      // team templates - protected by permissions
      {
        path: paths.dashboard.teams.root,
        element: (
          <PermissionBasedGuard permission="get-templates-teams">
            <TemplatesTeamPage />
          </PermissionBasedGuard>
        ),
      },
      {
        path: paths.dashboard.teams.my_team_templates,
        element: (
          <PermissionBasedGuard permission="get-templates-team">
            <MyTeamsTemplatesPage />
          </PermissionBasedGuard>
        ),
      },
      {
        path: paths.dashboard.teams.templates,
        element: (
          <PermissionBasedGuard permission="get-templates-teams">
            <TemplatesTeamPage />
          </PermissionBasedGuard>
        ),
      },

      {
        path: paths.dashboard.knowledgeBase,
        element: <ConnectionsIntegrationsPage />,
      },
      {
        path: paths.dashboard.settings,
        element: <SettingsPage />,
      },
      {
        path: paths.dashboard.scheduled.root,
        element: (
          <PermissionBasedGuard permission="get-tasks">
            <ScheduledTasksPage />
          </PermissionBasedGuard>
        ),
      },
    ],
  },
  // Routes without sidebar
  {
    path: paths.dashboard.root,
    element: CONFIG.auth.skip ? (
      <>{layoutContentNoSidebar}</>
    ) : (
      <AuthGuard>{layoutContentNoSidebar}</AuthGuard>
    ),
    children: [
      // pages for agents
      { path: paths.dashboard.agents.clone(':id'), element: <AgentsClonePage /> },
      { path: paths.dashboard.agents.reconfigure(':id', ':agentId'), element: <AgentsClonePage /> },
      { path: paths.dashboard.agents.chat(':id', ':agentId', ':cid'), element: <AgentsChatPage /> },
      {
        path: paths.dashboard.agents.configureTool,
        element: agentOrTeam === 'team' ? <ConfigureteamToolsPage /> : <ConfigureToolsPage />,
      },

      {
        path: paths.dashboard.agents.create,
        element: <CreateAgentPage />,
      },
      { path: paths.dashboard.agents.edit, element: <CreateAgentPage /> },

      // pages for team templates
      { path: paths.dashboard.teams.clone(':id'), element: <TeamTemplatessClonePage /> },
      {
        path: paths.dashboard.teams.reconfigure(':id', ':myTeamId'),
        element: <TeamTemplatessClonePage />,
      },
      {
        path: paths.dashboard.teams.chat(':id', ':agentId', ':cid'),
        element: <TeamTemplatesChat />,
      },

      { path: paths.dashboard.teams.create, element: <CreateTeanTeamplatePage /> },
      { path: paths.dashboard.teams.edit, element: <CreateTeanTeamplatePage /> },
      // { path: paths.dashboard.agents.configureTool, element:  },

      // {
      //   path: paths.dashboard.agents.chat_messages_events(':id', ':agentId', ':cid'),
      //   element: <ChatMessagesEventPage />,
      // },
    ],
  },
  // {
  //   path: 'dashboard/profile',
  //   element: CONFIG.auth.skip ? (
  //     <ProfileLayout>
  //       <Suspense fallback={<LoadingScreen />}>
  //         <Outlet />
  //       </Suspense>
  //     </ProfileLayout>
  //   ) : (
  //     <AuthGuard>
  //       <ProfileLayout>
  //         <Suspense fallback={<LoadingScreen />}>
  //           <Outlet />
  //         </Suspense>
  //       </ProfileLayout>
  //     </AuthGuard>
  //   ),
  //   children: [
  //     { element: <ProfilePage />, index: true },
  //     { path: 'knowledge-base', element: <KnowledgeBasePage /> },
  //     { path: 'preferences', element: <PreferencesPage /> },
  //     { path: 'settings', element: <SettingsPage /> },
  //   ],
  // },
];
