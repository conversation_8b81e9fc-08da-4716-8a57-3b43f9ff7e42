export const LLM_MODEL_OPTIONS = [
  {
    value: 'GPT_4O_MINI',
    label: 'GPT-4o Mini',
    description: 'Fast and efficient model for simple tasks',
    icon: 'hugeicons:chat-gpt',
    provider: 'OpenAI',
  },
  {
    value: 'GPT_4O',
    label: 'GPT-4o',
    description: 'Advanced model for complex reasoning tasks',
    icon: 'arcticons:openai-chatgpt',
    provider: 'OpenAI',
  },
  {
    value: 'CLAUDE_3_7_SONNET',
    label: 'Claude 3.5 Sonnet',
    description: "Anthropic's most intelligent model for complex tasks",
    icon: 'simple-icons:anthropic',
    provider: 'Anthropic',
  },
  {
    value: 'GEMINI_2_0_FLASH',
    label: 'Gemini 2.0 Flash',
    description: "Google's latest multimodal AI model",
    icon: 'ri:gemini-fill',
    provider: 'Google',
  },
  {
    value: 'GEMINI_1_5_FLASH',
    label: 'Gemini 1.5 Flash',
    description: 'Fast and efficient multimodal model',
    icon: 'cbi:gemini',
    provider: 'Google',
  },
  {
    value: 'GEMINI_25_FLASH',
    label: 'Gemini 25 Flash',
    description: 'Fast and efficient multimodal model',
    icon: 'material-icon-theme:gemini-ai',
    provider: 'Google',
  },
  {
    value: 'GEMINI_25_FLASH_LITE',
    label: 'Gemini 25 Flash Lite',
    description: 'Fast and efficient multimodal model',
    icon: 'logos:google-gemini',
    provider: 'Google',
  },
  {
    value: 'GEMINI_15_PRO',
    label: 'Gemini 15 Pro',
    description: 'Fast and efficient multimodal model',
    icon: 'simple-icons:googlegemini',
    provider: 'Google',
  },
  {
    value: 'GEMINI_25_PRO',
    label: 'Gemini 25 Pro',
    description: 'Fast and efficient multimodal model',
    icon: 'cbi:gemini',
    provider: 'Google',
  },
] as const;

export const MODEL_VALUES = [
  'GPT_4O_MINI',
  'GPT_4O',
  'CLAUDE_3_7_SONNET',
  'GEMINI_2_0_FLASH',
  'GEMINI_1_5_FLASH',
  'GEMINI_25_PRO',
  'GEMINI_15_PRO',
  'GEMINI_25_FLASH_LITE',
  'GEMINI_25_FLASH',
] as const;
