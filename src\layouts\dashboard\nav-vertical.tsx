import { useCallback, useState, useRef } from 'react';
import type { Breakpoint } from '@mui/material/styles';
import type { NavSectionProps } from 'src/components/nav-section';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import { useTheme, useColorScheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Avatar from '@mui/material/Avatar';
import LinearProgress from '@mui/material/LinearProgress';
import IconButton from '@mui/material/IconButton';
import Popper from '@mui/material/Popper';
import Paper from '@mui/material/Paper';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import Chip from '@mui/material/Chip';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';

import { varAlpha, hideScrollY } from 'src/theme/styles';

import { useAuthContext, useMockedUser } from 'src/auth/hooks';
import { signOut } from 'src/auth/context/jwt';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useSettingsContext } from 'src/components/settings';
import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';
import { NavSectionMini, NavSectionVertical } from 'src/components/nav-section';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';

// import { NavUpgrade } from '../components/nav-upgrade';

// ----------------------------------------------------------------------

export type NavVerticalProps = NavSectionProps & {
  isNavMini: boolean;
  layoutQuery: Breakpoint;
  onToggleNav: () => void;
  slots?: {
    topArea?: React.ReactNode;
    bottomArea?: React.ReactNode;
  };
};

export function NavVertical({
  sx,
  data,
  slots,
  isNavMini,
  layoutQuery,
  onToggleNav,
  ...other
}: NavVerticalProps) {
  const theme = useTheme();
  const router = useRouter();
  const { t } = useTranslation();
  const { user } = useMockedUser();
  const settings = useSettingsContext();
  const { setMode } = useColorScheme();

  // State for logout confirmation dialog
  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);
  // State for search query
  const [searchQuery, setSearchQuery] = useState('');
  // State for profile dropdown menu
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const profileButtonRef = useRef<HTMLButtonElement>(null);
  const miniProfileButtonRef = useRef<HTMLDivElement>(null);
  // Get current theme mode from settings
  const isDarkMode = settings.colorScheme === 'dark';

  // Handle opening the logout confirmation dialog
  const handleOpenLogoutDialog = () => {
    setOpenLogoutDialog(true);
    setProfileMenuOpen(false); // Close profile menu when opening logout dialog
  };

  // Handle closing the logout confirmation dialog
  const handleCloseLogoutDialog = () => {
    setOpenLogoutDialog(false);
  };

  // Handle profile menu toggle
  const handleProfileMenuToggle = () => {
    setProfileMenuOpen((prev) => !prev);
  };

  // Handle profile menu close
  const handleProfileMenuClose = () => {
    setProfileMenuOpen(false);
  };

  // Handle settings navigation
  const handleSettingsClick = () => {
    setProfileMenuOpen(false);
    router.push(paths.dashboard.settings);
  };

  // Handle keyboard navigation for profile menu
  const handleProfileMenuKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setProfileMenuOpen(false);
      profileButtonRef.current?.focus();
    }
  };
  const { checkUserSession } = useAuthContext();

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle setting light theme
  const handleSetLightTheme = () => {
    settings.onUpdateField('colorScheme', 'light');
    setMode('light');
  };

  // Handle setting dark theme
  const handleSetDarkTheme = () => {
    settings.onUpdateField('colorScheme', 'dark');
    setMode('dark');
  };

  // Handle logout after confirmation
  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();

      setOpenLogoutDialog(false);
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error during logout:', error);
    }
  }, [checkUserSession, router, setOpenLogoutDialog]);
  const renderNavVertical = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ px: 3, pt: 3, pb: 2 }}>
          {/* Header with Workforces title and collapse button */}
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Logo />
              <Typography variant="h5" sx={{ fontWeight: 700, color: 'text.primary' }}>
                Workforces
              </Typography>
            </Stack>
            <IconButton
              onClick={onToggleNav}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'action.hover',
                },
              }}
            >
              <Iconify icon="ri:side-bar-fill" sx={{ width: 20, height: 20 }} />
            </IconButton>
          </Stack>

          {/* Search Input */}
          <TextField
            fullWidth
            size="small"
            placeholder="Search"
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon="eva:search-fill"
                    sx={{ color: 'text.disabled', width: 20, height: 20 }}
                  />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                bgcolor: isDarkMode ? '#3a3841' : 'background.paper',
                '& fieldset': {
                  borderColor: isDarkMode ? '#4a4851' : 'divider',
                },
                '&:hover fieldset': {
                  borderColor: 'primary.main',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
                '& input': {
                  color: isDarkMode ? '#ffffff' : 'inherit',
                },
                '& input::placeholder': {
                  color: isDarkMode ? '#9ca3af' : 'inherit',
                  opacity: 1,
                },
              },
            }}
          />
        </Box>
      )}

      <Scrollbar fillContent>
        <NavSectionVertical data={data} sx={{ px: 2, flex: '1 1 auto' }} {...other} />

        {/* User Profile Section */}
        <Box sx={{ p: 2.5, mt: 'auto' }}>
          <Box
            sx={{
              bgcolor: isDarkMode ? '#3a3841' : 'background.paper',
              borderRadius: 3,
              p: 2.5,
              boxShadow: isDarkMode
                ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                : '0 2px 8px rgba(0, 0, 0, 0.08)',
              border: isDarkMode ? '1px solid #4a4851' : 'none',
            }}
          >
            <Stack spacing={2.5}>
              {/* User Info with Arrow */}
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 40, height: 40 }}>
                  {user?.displayName?.charAt(0)}
                </Avatar>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant="subtitle2"
                    noWrap
                    sx={{ fontWeight: 600, color: 'text.primary' }}
                  >
                    {user?.displayName || 'James Broeng'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </Box>
                <IconButton
                  ref={profileButtonRef}
                  size="small"
                  onClick={handleProfileMenuToggle}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleProfileMenuToggle();
                    }
                  }}
                  aria-label="Profile menu"
                  aria-expanded={profileMenuOpen}
                  aria-haspopup="true"
                  sx={{
                    color: 'text.secondary',
                    transform: profileMenuOpen ? 'rotate(90deg)' : 'rotate(0deg)',
                    transition: 'transform 0.2s ease-in-out',
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }}
                >
                  <Iconify icon="eva:arrow-ios-forward-fill" sx={{ width: 16, height: 16 }} />
                </IconButton>
              </Stack>
              <Stack>
                <AppButton variant="outlined" name="Upgrade to Pro" label="Upgrade to Pro" />
              </Stack>
              <Divider />
              {/* Progress Section with Trophy Icon */}
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: 1.5,
                    bgcolor: isDarkMode ? '#4a4851' : 'grey.100',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Iconify
                    icon="solar:cup-outline"
                    sx={{ width: 24, height: 24, color: 'darkgray' }}
                  />
                </Box>

                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5, whiteSpace: 'nowrap' }}
                  >
                    1,462 Available{' '}
                    <span style={{ color: '#9CA3AF', fontWeight: 400 }}>Free Plan</span>
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={50}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      bgcolor: isDarkMode ? '#4a4851' : 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 3,
                        bgcolor: 'primary.main',
                      },
                    }}
                  />
                </Box>
              </Stack>
            </Stack>
          </Box>
        </Box>

        {/* Theme Toggle Buttons */}
        <Box
          sx={{
            bgcolor: isDarkMode ? '#3a3841' : '#F3F4F6',
            borderRadius: 3,
            p: 0.5,
            display: 'flex',
            gap: 0.5,
            width: '90%',
            marginLeft: 'auto',
            marginRight: 'auto',
            mb: '10px',
            border: isDarkMode ? '1px solid #4a4851' : 'none',
          }}
        >
          <Box
            sx={{
              flex: 1,
              bgcolor: !isDarkMode ? 'white' : 'transparent',
              borderRadius: 2.5,
              py: 0.5,
              px: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: !isDarkMode ? '0 1px 3px rgba(0, 0, 0, 0.1)' : 'none',
              '&:hover': {
                bgcolor: !isDarkMode ? 'white' : '#4a4851',
              },
            }}
            onClick={handleSetLightTheme}
          >
            <Iconify
              icon="solar:sun-bold"
              sx={{
                width: 18,
                height: 18,
                color: !isDarkMode ? 'text.primary' : 'text.secondary',
              }}
            />
          </Box>
          <Box
            sx={{
              flex: 1,
              bgcolor: isDarkMode ? '#5a5a6b' : 'transparent',
              borderRadius: 2.5,
              py: 1.5,
              px: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: isDarkMode ? '0 1px 3px rgba(0, 0, 0, 0.2)' : 'none',
              '&:hover': {
                bgcolor: isDarkMode ? '#6a6a7b' : '#E5E7EB',
              },
            }}
            onClick={handleSetDarkTheme}
          >
            <Iconify
              icon="solar:moon-bold"
              sx={{
                width: 18,
                height: 18,
                color: isDarkMode ? 'text.primary' : 'text.secondary',
              }}
            />
          </Box>
        </Box>

        {slots?.bottomArea}
      </Scrollbar>
    </>
  );

  const renderNavMini = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 2.5 }}>
          {/* Collapse Icon */}
          <IconButton
            onClick={onToggleNav}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Iconify icon="ri:side-bar-fill" sx={{ width: 20, height: 20 }} />
          </IconButton>
        </Box>
      )}

      <NavSectionMini
        data={data}
        sx={{ pb: 2, px: 0.5, ...hideScrollY, flex: '1 1 auto', overflowY: 'auto' }}
        {...other}
      />

      {/* Mini User Profile Section */}
      <Box sx={{ p: 1.5, mt: 'auto' }}>
        <Stack spacing={2} alignItems="center">
          {/* User Avatar with Online Status - Clickable */}
          <Box
            ref={miniProfileButtonRef}
            onClick={handleProfileMenuToggle}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleProfileMenuToggle();
              }
            }}
            tabIndex={0}
            role="button"
            aria-label="Profile menu"
            aria-expanded={profileMenuOpen}
            aria-haspopup="true"
            sx={{
              position: 'relative',
              cursor: 'pointer',
              borderRadius: '50%',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.05)',
                boxShadow: (theme) =>
                  theme.palette.mode === 'dark'
                    ? '0 4px 12px rgba(0, 0, 0, 0.4)'
                    : '0 4px 12px rgba(0, 0, 0, 0.15)',
              },
              '&:focus': {
                outline: '2px solid',
                outlineColor: 'primary.main',
                outlineOffset: '2px',
              },
            }}
          >
            <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 40, height: 40 }}>
              {user?.displayName?.charAt(0)}
            </Avatar>
            {/* Online Status Indicator */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: 12,
                height: 12,
                bgcolor: '#10B981', // Green color for online status
                borderRadius: '50%',
                border: '2px solid white',
              }}
            />
          </Box>

          {/* Theme Toggle Button */}
          <IconButton
            size="small"
            onClick={isDarkMode ? handleSetLightTheme : handleSetDarkTheme}
            sx={{
              bgcolor: isDarkMode ? '#3a3841' : 'grey.100',
              color: 'text.secondary',
              border: isDarkMode ? '1px solid #4a4851' : 'none',
              '&:hover': {
                bgcolor: isDarkMode ? '#4a4851' : 'grey.200',
              },
            }}
          >
            <Iconify
              icon={isDarkMode ? 'solar:sun-bold' : 'solar:moon-bold'}
              sx={{ width: 18, height: 18 }}
            />
          </IconButton>
        </Stack>
      </Box>

      {slots?.bottomArea}
    </>
  );

  return (
    <Box
      sx={{
        my: '10px',
        top: 0,
        left: 0,
        height: 1,
        position: 'fixed',
        flexDirection: 'column',
        bgcolor: isDarkMode ? '#2a2831' : '#F8F9FA', // Dark mode: darker sidebar, Light mode: light gray
        zIndex: 'var(--layout-nav-zIndex)',
        width: isNavMini ? 'var(--layout-nav-mini-width)' : 'var(--layout-nav-vertical-width)',
        borderRight: isDarkMode
          ? `1px solid ${varAlpha(theme.vars.palette.grey['800Channel'], 0.3)}`
          : `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
        borderRadius: '0 16px 16px 0', // Rounded right corners to match Figma
        transition: theme.transitions.create(['width'], {
          easing: 'var(--layout-transition-easing)',
          duration: 'var(--layout-transition-duration)',
        }),
        // Show on all screen sizes, but collapsed on smaller screens
        display: 'flex',
        // Add subtle shadow for depth like in Figma
        boxShadow: isDarkMode ? '0 4px 20px rgba(0, 0, 0, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.08)',
        ...sx,
      }}
    >
      {isNavMini ? renderNavMini : renderNavVertical}

      {/* Profile Dropdown Menu - Unified for Both Sidebar States */}
      <Popper
        open={profileMenuOpen}
        anchorEl={isNavMini ? miniProfileButtonRef.current : profileButtonRef.current}
        placement="right-start"
        transition
        disablePortal
        sx={{ zIndex: isNavMini ? 1400 : 1300 }} // Higher z-index for mini mode
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 8],
            },
          },
        ]}
      >
        {({ TransitionProps }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin: isNavMini ? 'left center' : 'bottom right',
            }}
          >
            <Paper
              sx={{
                width: 280,
                bgcolor: 'background.paper',
                borderRadius: 2,
                boxShadow: (theme) =>
                  theme.palette.mode === 'dark'
                    ? '0 8px 32px rgba(0, 0, 0, 0.4)'
                    : '0 8px 32px rgba(0, 0, 0, 0.12)',
                border: '1px solid',
                borderColor: 'divider',
                overflow: 'hidden',
                mb: isNavMini ? 0 : 1,
                ml: isNavMini ? 1 : 0,
              }}
            >
              <ClickAwayListener onClickAway={handleProfileMenuClose}>
                <Box onKeyDown={handleProfileMenuKeyDown}>
                  {/* User Info Header */}
                  <Box sx={{ p: 2.5, pb: 2 }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Avatar
                        src={user?.photoURL}
                        alt={user?.displayName}
                        sx={{ width: 48, height: 48 }}
                      >
                        {user?.displayName?.charAt(0)}
                      </Avatar>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography
                          variant="subtitle1"
                          noWrap
                          sx={{ fontWeight: 600, color: 'text.primary' }}
                        >
                          {user?.displayName || 'James Broeng'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {user?.email || '<EMAIL>'}
                        </Typography>
                      </Box>
                    </Stack>
                  </Box>

                  <Divider />

                  {/* Menu Items */}
                  <MenuList sx={{ py: 1 }}>
                    {/* New Version Available */}
                    {/* <MenuItem
                      sx={{
                        px: 2.5,
                        py: 0.5,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <Box
                          sx={{
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Iconify
                            icon="solar:download-bold"
                            sx={{ width: 18, height: 18, color: 'darkgray' }}
                          />
                        </Box>
                      </ListItemIcon>
                      <ListItemText
                        primary="New version available"
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                        }}
                      />
                    </MenuItem>
                    <Divider /> */}
                    {/* Settings */}
                    <MenuItem
                      onClick={handleSettingsClick}
                      sx={{
                        px: 2.5,
                        py: 0.5,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <Iconify
                          icon="fluent:settings-20-regular"
                          sx={{ width: 20, height: 20, color: 'text.secondary' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('settings.title')}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                        }}
                      />
                    </MenuItem>

                    <Divider sx={{ my: 1 }} />

                    {/* Logout */}
                    <MenuItem
                      onClick={handleOpenLogoutDialog}
                      sx={{
                        px: 2.5,
                        py: 0.5,
                        '&:hover': {
                          bgcolor: 'error.main',
                          '& .MuiListItemIcon-root, & .MuiListItemText-primary': {
                            color: 'error.contrastText',
                          },
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <Iconify
                          icon="fluent:sign-out-20-regular"
                          sx={{ width: 20, height: 20, color: 'error.main' }}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={t('settings.logout')}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 600,
                          color: 'error.main',
                        }}
                      />
                    </MenuItem>
                  </MenuList>
                </Box>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>

      {/* Logout Confirmation Dialog */}
      <ConfirmDialog
        open={openLogoutDialog}
        onClose={handleCloseLogoutDialog}
        close={handleCloseLogoutDialog}
        title={
          <Typography variant="h3" textAlign="center">
            {t('components.dialogs.logout')}
          </Typography>
        }
        content={<Typography variant="body1">{t('components.dialogs.logoutConfirm')}</Typography>}
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'warning.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.accountMenu.logout')}
              variant="contained"
              color="primary"
              onClick={handleLogout}
            />
            <AppButton
              sx={{ width: '45%', backgroundColor: 'white' }}
              label={t('components.buttons.cancel')}
              variant="outlined"
              color="inherit"
              onClick={handleCloseLogoutDialog}
            />
          </Stack>
        }
      />
    </Box>
  );
}
